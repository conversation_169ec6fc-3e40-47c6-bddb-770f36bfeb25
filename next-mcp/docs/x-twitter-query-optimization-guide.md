# X/Twitter Query Optimization Guide

This guide provides best practices for constructing high-quality X/Twitter search queries using the unified API tool with emphasis on industry keywords, qualifiers, and exclusions.

## Table of Contents
1. [Query Construction Basics](#query-construction-basics)
2. [Industry Keywords](#industry-keywords)
3. [Quality Qualifiers](#quality-qualifiers)
4. [Exclusion Filters](#exclusion-filters)
5. [Advanced Query Techniques](#advanced-query-techniques)
6. [Example Queries](#example-queries)
7. [Best Practices](#best-practices)

## Query Construction Basics

The X/Twitter API supports advanced search syntax that allows you to create precise, high-quality queries. The key components are:

- **Keywords**: The main search terms (industry-specific terms are most effective)
- **Qualifiers**: Filters that ensure quality (min_faves, min_retweets, etc.)
- **Exclusions**: Negative filters to remove noise and spam

### Basic Query Structure
```
[keywords] [qualifiers] [exclusions]
```

Example:
```
"SEO trends" min_faves:50 -is:retweet lang:en
```

## Industry Keywords

Prioritize industry-standard abbreviations and commonly used professional terms:

### SEO Keywords
- **Core**: SEO, SERP, SEM, "search engine optimization"
- **Technical**: CTR, CPC, PPC, CPM, ROAS, "organic traffic"
- **Tools**: GA4, GTM, GSC, "Google Analytics", "Search Console"
- **Concepts**: backlinks, keywords, ranking, indexing, crawling, "schema markup"

### SaaS Keywords
- **Core**: SaaS, "software as a service", "cloud software"
- **Metrics**: MRR, ARR, CAC, LTV, "churn rate", retention
- **Growth**: PLG, "product led growth", freemium, "trial conversion"
- **Business**: B2B, B2C, enterprise, SMB, startup

### Tech Keywords
- **Core**: API, SDK, framework, library, platform
- **Frontend**: UI, UX, React, Vue, Angular, frontend
- **Backend**: backend, database, server, microservices, REST, GraphQL
- **Emerging**: AI, ML, LLM, GPT, blockchain, Web3

### Marketing Keywords
- **Core**: marketing, "digital marketing", "growth marketing"
- **Metrics**: ROI, KPI, "conversion rate", CTR, engagement
- **Tactics**: CRO, "A/B testing", "funnel optimization", personalization
- **Channels**: "content marketing", "email marketing", "social media", "paid ads"

## Quality Qualifiers

Use these qualifiers to filter for high-quality content:

### Engagement Qualifiers
- `min_faves:50` - Minimum 50 likes
- `min_retweets:10` - Minimum 10 retweets
- `min_replies:5` - Minimum 5 replies (good for discussions)

### Content Qualifiers
- `has:media` - Contains images or videos
- `has:links` - Contains external links (good for articles)
- `-has:links` - No external links (pure discussions)
- `has:mentions` - Contains @mentions

### User Qualifiers
- `from:verified` - Only from verified accounts
- `from:username` - From specific user
- `to:username` - Replies to specific user
- `filter:follows` - From accounts you follow

### Time Qualifiers
- `since:2024-01-01` - Tweets since date
- `until:2024-12-31` - Tweets until date
- `within_time:1h` - Within last hour/day/week

### Language Qualifiers
- `lang:en` - English only
- `lang:es` - Spanish only

## Exclusion Filters

Remove noise and improve result quality:

### Spam Exclusions
```
-spam -"follow me" -"follow back" -"check my bio"
```

### Promotional Exclusions
```
-giveaway -contest -"win a" -promotion -discount
```

### Low Quality Exclusions
```
-"just posted" -"link in bio" -"check out" -"new video"
```

### Topic-Specific Exclusions
```
-crypto -NFT -"to the moon" -airdrop  # For non-crypto searches
-NSFW -"18+" -OnlyFans               # For professional content
```

### Always Recommended
```
-is:retweet -is:reply                 # Original content only
```

## Advanced Query Techniques

### Boolean Operators
- **AND** (implicit): `SEO trends` (both terms required)
- **OR**: `SEO OR SEM` (either term)
- **NOT**: `-keyword` (exclude term)
- **Grouping**: `(SEO OR SEM) marketing`

### Exact Phrase Matching
```
"exact phrase here"
```

### Wildcard Searches
```
market*  # Matches marketing, marketplace, etc.
```

### Complex Queries
```
("SEO trends" OR "search engine optimization") min_faves:100 from:verified -crypto lang:en
```

## Example Queries

### Finding SEO Trends
```
"SEO trends" OR "SEO 2025" min_faves:50 min_retweets:10 -is:retweet lang:en
```

### High-Quality SaaS Discussions
```
(SaaS OR "software as a service") AND (growth OR scaling) min_replies:10 min_faves:30 -crypto lang:en
```

### AI Marketing Insights from Experts
```
AI "marketing automation" from:verified min_faves:100 has:links -spam -promotional lang:en
```

### Recent Technical SEO News
```
"Core Web Vitals" OR "page speed" SEO since:2024-01-01 min_faves:20 has:media lang:en
```

### Finding Industry Authorities
```
SEO min_faves:200 min_retweets:50 from:verified -is:retweet lang:en
```

## Best Practices

### 1. Start with Industry Keywords
- Use professional abbreviations (SEO, SaaS, API)
- Combine with specific concepts
- Avoid overly generic terms

### 2. Apply Quality Filters Progressively
- Start with `min_faves:10` for broader results
- Increase to `min_faves:50` or `min_faves:100` for higher quality
- Add `from:verified` for authoritative content

### 3. Use Exclusions Wisely
- Always exclude retweets: `-is:retweet`
- Add spam filters for cleaner results
- Exclude irrelevant topics (crypto, promotional content)

### 4. Optimize for Your Goal
- **For trends**: Use recent timeframes and high engagement
- **For discussions**: Focus on `min_replies`
- **For resources**: Use `has:links`
- **For visual content**: Use `has:media`

### 5. Iterate and Refine
- Start broad, then narrow down
- Analyze initial results to identify patterns
- Add discovered keywords to refine further

## Query Quality Indicators

When evaluating query effectiveness, look for:

- **Average engagement > 100**: Excellent quality
- **Average engagement 50-100**: Good quality
- **Average engagement < 50**: Consider adding more filters
- **Result count < 10**: Query might be too restrictive
- **Result count > 100**: Consider adding quality filters

## Common Pitfalls to Avoid

1. **Over-filtering**: Too many restrictions yield no results
2. **Generic keywords**: "marketing" vs "B2B SaaS marketing"
3. **Missing language filter**: Always specify `lang:en` or appropriate language
4. **Forgetting exclusions**: Always exclude retweets and spam
5. **Wrong date format**: Use YYYY-MM-DD format

## Integration with X/Twitter Unified Tool

When using the unified tool, construct queries like:

```json
{
  "endpoint": "tweet/advanced_search",
  "params": {
    "query": "SEO \"best practices\" min_faves:100 -is:retweet lang:en",
    "queryType": "Top"
  },
  "format": "analyzed"
}
```

The tool will provide quality metrics and optimization suggestions based on the results.