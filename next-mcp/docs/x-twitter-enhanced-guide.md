# X/Twitter Enhanced API Tool Guide

The Enhanced X/Twitter API tool combines the flexibility of the unified API access with an integrated query builder for automatic query optimization. This guide explains how to use the enhanced features effectively.

## Overview

The enhanced tool provides:
- Access to all TwitterAPI.io endpoints
- Integrated query builder for optimized searches
- Industry-specific keyword prioritization
- Quality presets and noise exclusion
- Query templates for common use cases
- Automatic query analysis and optimization suggestions

## Key Features

### 1. Integrated Query Builder

The tool includes a built-in query builder accessible through the `build-query` endpoint:

```json
{
  "endpoint": "build-query",
  "queryBuilder": {
    "action": "build",
    "keywords": ["SEO", "backlinks"],
    "qualityPreset": "highEngagement",
    "excludeNoise": true
  }
}
```

### 2. Query Builder Actions

#### Build Custom Query (`action: "build"`)
Create optimized queries with your keywords:
```json
{
  "action": "build",
  "keywords": ["AI", "marketing automation"],
  "qualityPreset": "highEngagement",
  "excludeNoise": true
}
```

#### Use Templates (`action: "template"`)
Pre-built templates for common searches:
```json
{
  "action": "template",
  "template": "findExperts",
  "topic": "blockchain"
}
```

Available templates:
- `findExperts`: Find industry authorities
- `recentNews`: Get recent news on a topic
- `tutorials`: Find educational content
- `discussions`: Find active discussions

#### Industry-Specific Queries (`action: "industry"`)
Generate multiple queries for an industry:
```json
{
  "action": "industry",
  "industry": "SaaS",
  "qualityPreset": "moderate"
}
```

Supported industries:
- SEO
- SaaS
- Tech
- Marketing
- DevOps

#### Find Authorities (`action: "authority"`)
Search for industry experts:
```json
{
  "action": "authority",
  "industry": "Marketing"
}
```

#### Find Discussions (`action: "discussion"`)
Locate high-quality discussions:
```json
{
  "action": "discussion",
  "topic": "product led growth"
}
```

### 3. Quality Presets

The tool includes predefined quality settings:

- **highEngagement**: min_faves:100, min_retweets:20
- **moderate**: min_faves:50, min_retweets:10
- **minimal**: min_faves:10
- **verified**: from:verified, min_faves:20

### 4. Automatic Noise Exclusion

When `excludeNoise: true`, the tool automatically excludes:
- Spam content
- Promotional posts
- Low-quality content
- Giveaways and contests

## Usage Examples

### Example 1: High-Quality SEO Content Search

```json
{
  "endpoint": "build-query",
  "queryBuilder": {
    "action": "build",
    "keywords": ["SEO", "Core Web Vitals", "2025"],
    "qualityPreset": "highEngagement",
    "excludeNoise": true
  }
}
```

This generates a query like:
```
SEO "Core Web Vitals" 2025 min_faves:100 min_retweets:20 lang:en -is:retweet -spam -"follow me" -giveaway
```

Then use the generated query:
```json
{
  "endpoint": "tweet/advanced_search",
  "params": {
    "query": "[generated query]",
    "queryType": "Top"
  },
  "format": "analyzed"
}
```

### Example 2: Find SaaS Industry Authorities

```json
{
  "endpoint": "build-query",
  "queryBuilder": {
    "action": "authority",
    "industry": "SaaS"
  }
}
```

### Example 3: Recent AI News

```json
{
  "endpoint": "build-query",
  "queryBuilder": {
    "action": "template",
    "template": "recentNews",
    "topic": "AI regulation"
  }
}
```

### Example 4: Direct API Call with Manual Query

You can still use the tool for direct API calls:
```json
{
  "endpoint": "tweet/advanced_search",
  "params": {
    "query": "your custom query here",
    "queryType": "Latest"
  }
}
```

## Response Formats

### Simplified Format (Default)
- Clean, readable output
- Key metrics highlighted
- Engagement indicators

### Analyzed Format
- All simplified content plus:
- Query quality assessment
- Industry keywords found
- Optimization suggestions
- Engagement analysis

### Raw Format
- Complete API response
- Useful for debugging

## Query Analysis Features

When using `format: "analyzed"`, the tool provides:

1. **Quality Metrics**
   - Total and average engagement
   - Quality assessment rating

2. **Keyword Analysis**
   - Industry terms found in results
   - Keyword density insights

3. **Optimization Suggestions**
   - Specific improvements for your query
   - Pre-built query builder commands

## Best Practices

1. **Start with Query Builder**: Use the query builder to construct optimized queries automatically

2. **Choose Appropriate Quality Presets**: 
   - Use "highEngagement" for finding viral content
   - Use "moderate" for balanced results
   - Use "verified" for authoritative sources

3. **Leverage Industry Keywords**: The tool knows industry-specific terminology and will prioritize these in searches

4. **Exclude Noise**: Always set `excludeNoise: true` unless you specifically need unfiltered results

5. **Use Templates**: For common search patterns, templates provide optimized queries instantly

6. **Analyze Results**: Use `format: "analyzed"` to get insights and optimization suggestions

## Switching Between Tools

The project includes two versions of the X/Twitter tool:

1. **Standard Unified Tool** (`xtwitterUnified`): Direct API access
2. **Enhanced Tool** (`xtwitterEnhanced`): API access + query builder

To enable the enhanced version, update your configuration:
```javascript
availableTools: {
  xtwitterUnified: {
    enabled: false  // Disable standard version
  },
  xtwitterEnhanced: {
    enabled: true   // Enable enhanced version
  }
}
```

## Troubleshooting

### Query Builder Returns Empty Results
- Ensure keywords are provided for "build" action
- Check that industry/topic is specified for other actions
- Verify quality preset name is correct

### Low-Quality Results
- Use higher quality presets
- Enable noise exclusion
- Add more specific industry keywords
- Check the analyzed format for optimization suggestions

### API Errors
- Verify X_API_KEY is set correctly
- Check endpoint name spelling
- Ensure parameters match endpoint requirements

## Advanced Tips

1. **Combine Multiple Keywords**: The query builder intelligently combines keywords with appropriate operators

2. **Time-Based Searches**: Templates like "recentNews" automatically add time filters

3. **Authority Discovery**: Use the authority finder to identify key influencers in any industry

4. **Discussion Mining**: Find valuable conversations with the discussion finder

5. **Query Iteration**: Use the optimization suggestions from analyzed results to refine your queries

## Conclusion

The Enhanced X/Twitter API tool streamlines the process of finding high-quality, relevant content on X/Twitter by combining powerful API access with intelligent query construction. Whether you're researching industry trends, finding authorities, or monitoring discussions, the integrated query builder ensures you get the best possible results with minimal effort.