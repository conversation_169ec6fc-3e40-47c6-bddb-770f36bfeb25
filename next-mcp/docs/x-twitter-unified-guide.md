# X (Twitter) Unified API Tool Guide

A comprehensive guide for using the unified X (Twitter) API tool to find high-quality bloggers, analyze engagement, and discover trending content.

## Table of Contents

1. [Overview](#overview)
2. [Quick Start](#quick-start)
3. [Finding High-Quality Bloggers](#finding-high-quality-bloggers)
4. [Finding High-Engagement Posts](#finding-high-engagement-posts)
5. [Available Endpoints](#available-endpoints)
6. [Helper Functions](#helper-functions)
7. [Advanced Usage](#advanced-usage)
8. [Migration Guide](#migration-guide)

## Overview

The unified X (Twitter) API tool provides a single, flexible interface for all Twitter data operations, replacing the previous multi-tool approach with a more maintainable and extensible solution.

### Benefits of the Unified Approach

1. **Single Entry Point**: One tool (`x-api`) handles all Twitter operations
2. **Consistent Interface**: All endpoints follow the same pattern
3. **Flexible Response Formats**: Choose between raw, simplified, or analyzed data
4. **Better Maintainability**: Easier to update and extend functionality
5. **Reduced Complexity**: No need to remember multiple tool names

### Interface Generality

The tool maintains interface generality through:
- **Dynamic endpoint routing**: Specify any endpoint path
- **Flexible parameters**: Pass any parameters the endpoint supports
- **Format options**: Choose your preferred response format
- **Helper functions**: Built-in utilities for common tasks

### Response Formats

1. **Raw Format** (`format: "raw"`): Complete API response with all metadata
2. **Simplified Format** (`format: "simplified"`): Clean, essential data only
3. **Analyzed Format** (`format: "analyzed"`): Processed data with insights and metrics

## Quick Start

### Discovering Available Endpoints

List all available endpoints:

```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/help"
  }
});
```

### Basic Usage Examples

#### Search for tweets
```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/search/tweets",
    params: {
      query: "AI tools",
      maxResults: 20
    },
    format: "simplified"
  }
});
```

#### Get user profile
```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/users/by-username",
    params: {
      username: "elonmusk"
    },
    format: "analyzed"
  }
});
```

### Common Use Cases

1. **Content Discovery**: Find trending topics in your industry
2. **Influencer Identification**: Locate thought leaders and bloggers
3. **Engagement Analysis**: Measure content performance
4. **Competitor Research**: Track competitor activity
5. **Trend Monitoring**: Stay updated on emerging topics

## Finding High-Quality Bloggers

### Strategies for Identifying Quality Bloggers

1. **Engagement-Based Search**
   - Look for consistent high engagement rates
   - Check reply quality and discussion depth
   - Analyze follower-to-engagement ratios

2. **Content Quality Indicators**
   - Thread frequency and depth
   - Use of data/sources in posts
   - Original content vs. retweets ratio

3. **Network Analysis**
   - Who they interact with
   - Who mentions them
   - Community involvement

### Query Examples for Different Industries

#### Tech Bloggers
```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/search/users",
    params: {
      query: "tech blogger -influencer min_followers:5000",
      filters: {
        verified: true,
        hasAvatar: true
      }
    },
    format: "analyzed"
  }
});
```

#### Finance Bloggers
```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/search/tweets",
    params: {
      query: "from:list:finance-bloggers min_faves:100",
      maxResults: 50,
      timeRange: "7d"
    },
    format: "analyzed"
  }
});
```

#### Health & Wellness Bloggers
```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/search/users",
    params: {
      query: "wellness OR nutrition OR fitness bio:blogger",
      sortBy: "engagement_rate"
    },
    format: "analyzed"
  }
});
```

### Analyzing Blogger Metrics

```typescript
// Get detailed blogger analysis
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/users/analyze",
    params: {
      username: "target_blogger",
      metrics: ["engagement_rate", "post_frequency", "audience_quality"]
    },
    format: "analyzed"
  }
});
```

### Network Analysis Techniques

1. **Mutual Connections**
```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/network/connections",
    params: {
      username: "blogger_name",
      type: "mutual",
      minFollowers: 1000
    },
    format: "analyzed"
  }
});
```

2. **Community Detection**
```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/network/communities",
    params: {
      seedUsers: ["blogger1", "blogger2", "blogger3"],
      depth: 2
    },
    format: "analyzed"
  }
});
```

## Finding High-Engagement Posts

### Search Strategies for Viral Content

1. **Engagement Threshold Search**
```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/search/tweets",
    params: {
      query: "min_retweets:1000 min_faves:5000",
      timeRange: "24h"
    },
    format: "analyzed"
  }
});
```

2. **Velocity-Based Search**
```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/trending/velocity",
    params: {
      minVelocity: 100, // engagements per hour
      category: "tech"
    },
    format: "analyzed"
  }
});
```

### Engagement Filters and Thresholds

| Content Type | Min Likes | Min Retweets | Min Replies |
|-------------|-----------|--------------|-------------|
| Viral | 10,000 | 2,000 | 500 |
| High Engagement | 1,000 | 200 | 50 |
| Moderate | 100 | 20 | 10 |
| Niche Quality | 50 | 10 | 5 |

### Topic-Specific Searches

#### AI/ML Content
```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/search/tweets",
    params: {
      query: "(AI OR ML OR \"machine learning\") min_faves:500 -filter:retweets",
      lang: "en",
      sortBy: "engagement_rate"
    },
    format: "analyzed"
  }
});
```

#### Crypto/Web3
```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/search/tweets",
    params: {
      query: "(crypto OR web3 OR blockchain) min_retweets:100",
      excludeKeywords: ["scam", "pump"],
      verified: true
    },
    format: "analyzed"
  }
});
```

### Trend Analysis

```typescript
// Analyze trending topics
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/trends/analyze",
    params: {
      location: "worldwide",
      category: "technology",
      timeframe: "24h"
    },
    format: "analyzed"
  }
});
```

## Available Endpoints

### Search Endpoints

#### `/search/tweets`
Search for tweets with advanced filtering.

**Parameters:**
- `query` (required): Search query with operators
- `maxResults`: Number of results (1-100, default: 20)
- `sortBy`: Sort order (relevance, recency, engagement)
- `timeRange`: Time filter (1h, 24h, 7d, 30d)
- `lang`: Language code
- `excludeKeywords`: Array of keywords to exclude
- `includeMedia`: Only tweets with media
- `verified`: Only from verified accounts

**Example:**
```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/search/tweets",
    params: {
      query: "climate change min_faves:100",
      maxResults: 50,
      timeRange: "7d",
      lang: "en"
    },
    format: "simplified"
  }
});
```

#### `/search/users`
Search for users based on criteria.

**Parameters:**
- `query`: Search query
- `filters`: Object with filter criteria
- `sortBy`: Sort order (followers, engagement_rate, recent_activity)
- `minFollowers`: Minimum follower count
- `maxFollowers`: Maximum follower count

**Example:**
```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/search/users",
    params: {
      query: "developer advocate",
      filters: {
        verified: true,
        hasAvatar: true,
        minTweets: 100
      },
      minFollowers: 1000
    },
    format: "analyzed"
  }
});
```

### User Endpoints

#### `/users/by-username`
Get user profile by username.

**Parameters:**
- `username` (required): Twitter username without @
- `includeStats`: Include detailed statistics

**Example:**
```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/users/by-username",
    params: {
      username: "naval",
      includeStats: true
    },
    format: "analyzed"
  }
});
```

#### `/users/timeline`
Get user's recent tweets.

**Parameters:**
- `username` (required): Twitter username
- `maxResults`: Number of tweets (1-100)
- `excludeReplies`: Exclude reply tweets
- `excludeRetweets`: Exclude retweets

**Example:**
```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/users/timeline",
    params: {
      username: "paulg",
      maxResults: 20,
      excludeReplies: true,
      excludeRetweets: true
    },
    format: "simplified"
  }
});
```

### Analytics Endpoints

#### `/analytics/engagement`
Analyze engagement patterns.

**Parameters:**
- `tweets`: Array of tweet IDs or search query
- `metrics`: Array of metrics to calculate
- `groupBy`: Grouping option (hour, day, week)

**Example:**
```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/analytics/engagement",
    params: {
      tweets: "from:username",
      metrics: ["avg_engagement", "peak_times", "content_type_performance"],
      groupBy: "hour"
    },
    format: "analyzed"
  }
});
```

#### `/analytics/audience`
Analyze audience characteristics.

**Parameters:**
- `username` (required): Target username
- `sampleSize`: Number of followers to analyze
- `attributes`: Attributes to analyze

**Example:**
```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/analytics/audience",
    params: {
      username: "target_user",
      sampleSize: 1000,
      attributes: ["interests", "location", "activity_times"]
    },
    format: "analyzed"
  }
});
```

### Network Endpoints

#### `/network/connections`
Analyze user connections.

**Parameters:**
- `username` (required): Target username
- `type`: Connection type (followers, following, mutual)
- `minFollowers`: Minimum follower count for connections
- `limit`: Maximum results

**Example:**
```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/network/connections",
    params: {
      username: "user123",
      type: "mutual",
      minFollowers: 5000,
      limit: 100
    },
    format: "analyzed"
  }
});
```

#### `/network/influence`
Calculate influence metrics.

**Parameters:**
- `username` (required): Target username
- `depth`: Network depth to analyze (1-3)
- `metrics`: Specific metrics to calculate

**Example:**
```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/network/influence",
    params: {
      username: "influencer123",
      depth: 2,
      metrics: ["reach", "amplification", "network_quality"]
    },
    format: "analyzed"
  }
});
```

## Helper Functions

### Query Builder

The query builder helps construct complex search queries programmatically.

#### Basic Usage
```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/search/tweets",
    params: {
      query: {
        keywords: ["AI", "machine learning"],
        exclude: ["crypto", "NFT"],
        minLikes: 100,
        from: ["@openai", "@anthropic"],
        hasMedia: true
      }
    },
    format: "simplified"
  }
});
```

#### Advanced Query Building
```typescript
// Complex query with multiple conditions
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/search/tweets",
    params: {
      query: {
        all: ["climate", "change"],  // All keywords must be present
        any: ["solution", "innovation", "technology"],  // At least one
        exact: "carbon neutral",  // Exact phrase
        hashtags: ["ClimateAction", "NetZero"],
        minRetweets: 50,
        minLikes: 200,
        lang: "en",
        filter: {
          verified: true,
          hasLinks: true,
          mediaType: "video"
        }
      }
    },
    format: "analyzed"
  }
});
```

### Data Extraction Utilities

#### Extract Mentions
```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/utils/extract",
    params: {
      type: "mentions",
      text: "Check out @user1 and @user2 for great content!",
      returnType: "users"  // or "handles"
    }
  }
});
```

#### Extract Links
```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/utils/extract",
    params: {
      type: "links",
      tweets: ["tweet_id_1", "tweet_id_2"],
      expandUrls: true,
      filterDomains: ["medium.com", "substack.com"]
    }
  }
});
```

### Formatting Options

#### Custom Field Selection
```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/search/tweets",
    params: {
      query: "javascript tips"
    },
    format: "custom",
    fields: ["text", "author.username", "metrics.likes", "created_at"]
  }
});
```

#### Aggregation Options
```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/search/tweets",
    params: {
      query: "product launch"
    },
    format: "aggregated",
    aggregation: {
      groupBy: "author",
      metrics: ["total_engagement", "avg_engagement", "post_count"],
      sortBy: "total_engagement",
      limit: 10
    }
  }
});
```

### Analysis Functions

#### Sentiment Analysis
```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/analyze/sentiment",
    params: {
      query: "about:apple min_faves:100",
      timeRange: "7d",
      breakdown: true
    },
    format: "analyzed"
  }
});
```

#### Topic Modeling
```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/analyze/topics",
    params: {
      source: "from:username",
      numTopics: 5,
      minDocs: 10
    },
    format: "analyzed"
  }
});
```

## Advanced Usage

### Combining Multiple API Calls

#### Sequential Analysis Pipeline
```typescript
// Step 1: Find trending topics
const trends = await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/trends/current",
    params: { category: "tech" }
  }
});

// Step 2: Search for influencers in trending topics
const influencers = await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/search/users",
    params: {
      query: trends.data.topics[0].name + " expert",
      minFollowers: 10000
    }
  }
});

// Step 3: Analyze their recent content
const analysis = await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/users/batch-analyze",
    params: {
      usernames: influencers.data.users.map(u => u.username),
      metrics: ["engagement_rate", "topic_relevance"]
    }
  }
});
```

#### Parallel Data Collection
```typescript
// Collect data from multiple sources simultaneously
const sources = ["tech", "finance", "health"];
const results = await Promise.all(
  sources.map(category => 
    use_mcp_tool({
      server_name: "mcp-toolkit",
      tool_name: "x-api",
      arguments: {
        endpoint: "/search/tweets",
        params: {
          query: `${category} news min_faves:100`,
          maxResults: 50
        }
      }
    })
  )
);
```

### Rate Limiting Strategies

1. **Implement Request Delays**
```typescript
async function rateLimitedRequest(endpoint, params, delayMs = 1000) {
  await new Promise(resolve => setTimeout(resolve, delayMs));
  return use_mcp_tool({
    server_name: "mcp-toolkit",
    tool_name: "x-api",
    arguments: { endpoint, params }
  });
}
```

2. **Batch Processing**
```typescript
// Process large datasets in batches
async function processBatches(items, batchSize = 10) {
  const results = [];
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    const batchResults = await use_mcp_tool({
      server_name: "mcp-toolkit",
      tool_name: "x-api",
      arguments: {
        endpoint: "/batch/process",
        params: { items: batch }
      }
    });
    results.push(...batchResults.data);
    await new Promise(resolve => setTimeout(resolve, 2000)); // 2s delay between batches
  }
  return results;
}
```

### Error Handling

```typescript
async function safeApiCall(endpoint, params, retries = 3) {
  for (let i = 0; i < retries; i++) {
    try {
      const result = await use_mcp_tool({
        server_name: "mcp-toolkit",
        tool_name: "x-api",
        arguments: { endpoint, params }
      });
      
      if (result.error) {
        if (result.error.code === 'RATE_LIMIT') {
          // Wait and retry
          await new Promise(resolve => setTimeout(resolve, 60000)); // 1 minute
          continue;
        }
        throw new Error(result.error.message);
      }
      
      return result;
    } catch (error) {
      if (i === retries - 1) throw error;
      // Exponential backoff
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
    }
  }
}
```

### Performance Optimization

1. **Use Specific Fields**
```typescript
// Request only needed fields to reduce payload size
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/search/tweets",
    params: {
      query: "performance optimization",
      fields: ["id", "text", "metrics.likes"]  // Only request needed fields
    }
  }
});
```

2. **Enable Caching**
```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/users/by-username",
    params: {
      username: "cached_user",
      cache: {
        enabled: true,
        ttl: 3600  // Cache for 1 hour
      }
    }
  }
});
```

3. **Stream Large Results**
```typescript
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/search/tweets",
    params: {
      query: "large dataset",
      stream: true,
      onData: (chunk) => {
        // Process each chunk as it arrives
        console.log(`Received ${chunk.length} items`);
      }
    }
  }
});
```

## Migration Guide

### From Old Multi-Tool Approach

If you're migrating from the old multi-tool system, here's how to update your code:

#### Old: search-x-posts
```typescript
// Old way
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "search-x-posts",
  arguments: {
    query: "AI news",
    maxResults: 20
  }
});

// New way
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/search/tweets",
    params: {
      query: "AI news",
      maxResults: 20
    },
    format: "simplified"
  }
});
```

#### Old: analyze-x-topic
```typescript
// Old way
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "analyze-x-topic",
  arguments: {
    topic: "machine learning",
    timeRange: "7d"
  }
});

// New way
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/analyze/topic",
    params: {
      topic: "machine learning",
      timeRange: "7d"
    },
    format: "analyzed"
  }
});
```

#### Old: monitor-x-mentions
```typescript
// Old way
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "monitor-x-mentions",
  arguments: {
    keywords: ["brand", "product"],
    timeRange: "24h"
  }
});

// New way
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/monitor/mentions",
    params: {
      keywords: ["brand", "product"],
      timeRange: "24h"
    },
    format: "analyzed"
  }
});
```

#### Old: get-x-user-insights
```typescript
// Old way
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "get-x-user-insights",
  arguments: {
    username: "user123",
    tweetCount: 20
  }
});

// New way
await use_mcp_tool({
  server_name: "mcp-toolkit",
  tool_name: "x-api",
  arguments: {
    endpoint: "/users/insights",
    params: {
      username: "user123",
      tweetCount: 20
    },
    format: "analyzed"
  }
});
```

### Key Differences

1. **Single Tool Name**: Always use `x-api` instead of multiple tool names
2. **Endpoint Parameter**: Specify the operation via the `endpoint` parameter
3. **Params Object**: All parameters go inside the `params` object
4. **Format Option**: Explicitly choose your response format
5. **More Flexibility**: Access to more endpoints and options

### Benefits After Migration

- **Consistency**: Same pattern for all operations
- **Discoverability**: Use `/help` endpoint to explore available options
- **Future-Proof**: New features automatically available
- **Better Error Messages**: More descriptive error handling
- **Performance**: Optimized data fetching and caching

---

## Support and Resources

- **API Reference**: Use `/help` endpoint for complete API documentation
- **Examples Repository**: Check the examples folder for more use cases
- **Community**: Join discussions and share your use cases

Remember to always check rate limits and implement appropriate error handling in production applications.