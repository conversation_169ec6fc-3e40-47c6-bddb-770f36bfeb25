# Cursor MCP Configuration for DeepWiki

This project provides a DeepWiki MCP server for GitHub code search.

## MCP Server Configuration

Add this to your Cursor settings (Cursor > Settings > Features > Model Context Protocol):

```json
{
  "mcpServers": {
    "deepwiki": {
      "command": "npx",
      "args": ["@nxxt/deepwiki-mcp"],
      "env": {}
    }
  }
}
```

Or for local development:

```json
{
  "mcpServers": {
    "deepwiki-local": {
      "command": "node",
      "args": ["dist/index.js"],
      "cwd": "/path/to/your/next-mcp",
      "env": {
        "DEBUG": "1"
      }
    }
  }
}
```

## Available Tools

- `search-code`: Search for code implementations in GitHub repositories
- `deepwiki://info`: Get server information and capabilities
- `deepwiki://query/{queryId}`: Retrieve detailed query results
- `analyze-code`: Generate code analysis prompts
