{"name": "@nxxt/mcp-toolkit", "publishConfig": {"access": "public"}, "version": "1.3.0", "description": "Personal MCP Toolkit - Collection of useful MCP tools including DeepWiki, X/Twitter Search, BlackHatWorld Forum Search with Keyword Search, and more", "main": "dist/index.js", "type": "module", "bin": {"mcp-toolkit": "dist/index.js"}, "scripts": {"build": "tsc && chmod +x dist/index.js", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "eslint src", "lint:fix": "eslint src --fix", "prepublishOnly": "npm run build", "scraper:example": "tsx example.ts"}, "author": "nxxt <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/nxxt/mcp-toolkit#readme", "repository": {"type": "git", "url": "git+https://github.com/nxxt/mcp-toolkit.git"}, "bugs": {"url": "https://github.com/nxxt/mcp-toolkit/issues"}, "dependencies": {"@mendable/firecrawl-js": "^1.21.1", "@modelcontextprotocol/sdk": "^1.0.0", "cheerio": "^1.1.0", "commander": "^14.0.0", "dotenv": "^17.0.0", "playwright": "^1.40.0", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "tsx": "^4.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}, "files": ["dist", "src/blackhatworld"]}