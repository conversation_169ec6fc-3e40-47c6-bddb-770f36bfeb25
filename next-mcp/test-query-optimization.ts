import { XTwitterUnifiedClient } from './src/tools/x-twitter-unified';
import { XTwitterQueryBuilder, INDUSTRY_KEYWORDS, QUALITY_PRESETS, EXCLUSION_PATTERNS, QUERY_TEMPLATES } from './src/tools/x-twitter-query-builder';
import dotenv from 'dotenv';

dotenv.config();

async function testQueryOptimization() {
    console.log('🔍 X/Twitter Query Optimization Test\n');

    const client = new XTwitterUnifiedClient();

    // 1. Test industry-specific queries
    console.log('1️⃣ Testing Industry-Specific Queries\n');

    const industries: Array<keyof typeof INDUSTRY_KEYWORDS> = ['SEO', 'SaaS', 'Tech'];

    for (const industry of industries) {
        console.log(`\n📊 ${industry} Industry Queries:`);
        const queries = XTwitterQueryBuilder.getIndustryQueries(industry, 'moderate');

        queries.forEach((query, index) => {
            console.log(`   ${index + 1}. ${query}`);
        });

        // Test the first query
        console.log(`\n   Testing query: "${queries[0]}"`);
        try {
            const response = await client.request(
                'tweet/advanced_search',
                'GET',
                {
                    query: queries[0],
                    queryType: 'Top',
                    cursor: null
                }
            );

            if (response.data?.tweets) {
                const tweets = response.data.tweets;
                const totalEngagement = tweets.reduce((sum: number, t: any) =>
                    sum + (t.likeCount || 0) + (t.retweetCount || 0) + (t.replyCount || 0), 0
                );
                const avgEngagement = tweets.length > 0 ? Math.round(totalEngagement / tweets.length) : 0;

                console.log(`   ✅ Results: ${tweets.length} tweets, ${avgEngagement} avg engagement`);

                if (tweets.length > 0) {
                    console.log(`   📝 Sample tweet: "${tweets[0].text.substring(0, 100)}..."`);
                }
            }
        } catch (error) {
            console.error(`   ❌ Error: ${error}`);
        }

        await new Promise(resolve => setTimeout(resolve, 2000));
    }

    // 2. Test query templates
    console.log('\n\n2️⃣ Testing Query Templates\n');

    const testTopics = {
        findExperts: 'SEO',
        recentNews: 'AI',
        tutorials: 'React',
        discussions: 'SaaS pricing'
    };

    for (const [template, topic] of Object.entries(testTopics)) {
        const query = QUERY_TEMPLATES[template as keyof typeof QUERY_TEMPLATES](topic);
        console.log(`\n📋 ${template}("${topic}"):`);
        console.log(`   Query: ${query}`);

        try {
            const response = await client.request(
                'tweet/advanced_search',
                'GET',
                {
                    query,
                    queryType: template === 'recentNews' ? 'Latest' : 'Top',
                    cursor: null
                }
            );

            if (response.data?.tweets) {
                console.log(`   ✅ Found ${response.data.tweets.length} results`);
            }
        } catch (error) {
            console.error(`   ❌ Error: ${error}`);
        }

        await new Promise(resolve => setTimeout(resolve, 2000));
    }

    // 3. Test custom query building
    console.log('\n\n3️⃣ Testing Custom Query Building\n');

    const customQueries = [
        {
            name: 'High-engagement SEO content',
            options: {
                keywords: ['SEO', 'Google algorithm'],
                qualifiers: QUALITY_PRESETS.highEngagement,
                exclusions: [...EXCLUSION_PATTERNS.spam, ...EXCLUSION_PATTERNS.promotional]
            }
        },
        {
            name: 'SaaS growth discussions',
            options: {
                keywords: ['SaaS growth', 'MRR', 'churn'],
                qualifiers: { minReplies: 10, minLikes: 30, language: 'en' },
                operators: { useOR: true },
                exclusions: EXCLUSION_PATTERNS.crypto
            }
        },
        {
            name: 'AI marketing from verified accounts',
            options: {
                keywords: ['AI', 'marketing automation'],
                qualifiers: { ...QUALITY_PRESETS.verified, hasLinks: true },
                operators: { exactPhrase: true },
                exclusions: [...EXCLUSION_PATTERNS.spam, ...EXCLUSION_PATTERNS.lowQuality]
            }
        }
    ];

    for (const { name, options } of customQueries) {
        const query = XTwitterQueryBuilder.buildQuery(options);
        console.log(`\n🔧 ${name}:`);
        console.log(`   Query: ${query}`);

        try {
            const response = await client.request(
                'tweet/advanced_search',
                'GET',
                {
                    query,
                    queryType: 'Top',
                    cursor: null
                }
            );

            if (response.data?.tweets) {
                const tweets = response.data.tweets;
                console.log(`   ✅ Found ${tweets.length} results`);

                // Analyze quality
                const totalEngagement = tweets.reduce((sum: number, t: any) =>
                    sum + (t.likeCount || 0) + (t.retweetCount || 0) + (t.replyCount || 0), 0
                );
                const avgEngagement = tweets.length > 0 ? Math.round(totalEngagement / tweets.length) : 0;

                let quality = '⚠️ Low';
                if (avgEngagement > 500) quality = '🌟 Excellent';
                else if (avgEngagement > 100) quality = '✅ Good';
                else if (avgEngagement > 50) quality = '📊 Moderate';

                console.log(`   Quality: ${quality} (${avgEngagement} avg engagement)`);
            }
        } catch (error) {
            console.error(`   ❌ Error: ${error}`);
        }

        await new Promise(resolve => setTimeout(resolve, 2000));
    }

    // 4. Test authority finding
    console.log('\n\n4️⃣ Testing Authority Discovery\n');

    const authorityQuery = XTwitterQueryBuilder.buildAuthorityQuery('SEO');
    console.log(`\n🔍 Finding SEO authorities:`);
    console.log(`   Query: ${authorityQuery}`);

    try {
        const response = await client.request(
            'tweet/advanced_search',
            'GET',
            {
                query: authorityQuery,
                queryType: 'Top',
                cursor: null
            }
        );

        if (response.data?.tweets) {
            const tweets = response.data.tweets;
            const authors = new Map<string, { engagement: number, tweets: number }>();

            tweets.forEach((tweet: any) => {
                const username = tweet.author?.userName;
                const engagement = (tweet.likeCount || 0) + (tweet.retweetCount || 0) + (tweet.replyCount || 0);

                if (username) {
                    const current = authors.get(username) || { engagement: 0, tweets: 0 };
                    authors.set(username, {
                        engagement: current.engagement + engagement,
                        tweets: current.tweets + 1
                    });
                }
            });

            console.log(`   ✅ Found ${authors.size} potential authorities`);

            // Top 5 authorities
            const topAuthorities = Array.from(authors.entries())
                .map(([username, stats]) => ({
                    username,
                    avgEngagement: Math.round(stats.engagement / stats.tweets)
                }))
                .sort((a, b) => b.avgEngagement - a.avgEngagement)
                .slice(0, 5);

            console.log('\n   👑 Top Authorities:');
            topAuthorities.forEach((auth, index) => {
                console.log(`   ${index + 1}. @${auth.username} - ${auth.avgEngagement} avg engagement`);
            });
        }
    } catch (error) {
        console.error(`   ❌ Error: ${error}`);
    }

    // 5. Query optimization suggestions
    console.log('\n\n5️⃣ Query Optimization Analysis\n');

    const testQuery = 'SEO';  // Intentionally simple query
    console.log(`\n📊 Testing basic query: "${testQuery}"`);

    try {
        const response = await client.request(
            'tweet/advanced_search',
            'GET',
            {
                query: testQuery,
                queryType: 'Top',
                cursor: null
            }
        );

        if (response.data?.tweets) {
            const tweets = response.data.tweets;
            const totalEngagement = tweets.reduce((sum: number, t: any) =>
                sum + (t.likeCount || 0) + (t.retweetCount || 0) + (t.replyCount || 0), 0
            );
            const avgEngagement = tweets.length > 0 ? Math.round(totalEngagement / tweets.length) : 0;

            console.log(`   Results: ${tweets.length} tweets, ${avgEngagement} avg engagement`);

            if (avgEngagement < 50 && tweets.length > 10) {
                console.log('\n   💡 Optimization Suggestions:');
                console.log('   - Add quality filters: min_faves:50 min_retweets:10');
                console.log('   - Exclude noise: -is:retweet -is:reply');
                console.log('   - Target verified accounts: from:verified');
                console.log('   - Use specific terms: "SEO strategy" OR "SEO trends"');

                // Show optimized query
                const optimizedQuery = XTwitterQueryBuilder.buildQuery({
                    keywords: ['SEO strategy', 'SEO trends'],
                    qualifiers: QUALITY_PRESETS.moderate,
                    operators: { useOR: true },
                    exclusions: [...EXCLUSION_PATTERNS.spam, ...EXCLUSION_PATTERNS.promotional]
                });

                console.log(`\n   🚀 Optimized query: ${optimizedQuery}`);
            }
        }
    } catch (error) {
        console.error(`   ❌ Error: ${error}`);
    }

    console.log('\n\n✅ Query optimization test completed!');
}

// Run the test
testQueryOptimization().catch(console.error);