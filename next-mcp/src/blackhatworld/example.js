/**
 * BlackHatWorld MCP Tools Usage Examples
 * 
 * Demonstrates how to use the tools in real scenarios
 * Run with: node example.js
 */

import BHWMCPTools from './mcp-tools.js';

class BHWExamples {
    constructor() {
        this.tools = new BHWMCPTools();
    }

    /**
     * Example 1: Find recent high-quality threads about making money
     */
    async findRecentMoneyMakingThreads() {
        console.log('🔍 Example 1: Finding recent high-quality money-making threads\n');

        const result = await this.tools.bhwSearch({
            sections: ['making-money'],
            last_days: 7,
            order: 'replies',
            direction: 'desc',
            min_replies: 10,
            limit: 5
        });

        if (result.success) {
            console.log(`Found ${result.data.threads.length} high-quality threads:`);
            result.data.threads.forEach((thread, index) => {
                console.log(`${index + 1}. ${thread.title}`);
                console.log(`   📊 ${thread.replies} replies, ${thread.views} views`);
                console.log(`   👤 By: ${thread.author}`);
                console.log(`   🔗 ${thread.url}\n`);
            });
        } else {
            console.error('Search failed:', result.error);
        }
    }

    /**
     * Example 2: Get detailed content from a specific thread
     */
    async getDetailedThreadContent() {
        console.log('📄 Example 2: Getting detailed thread content\n');

        // First find a thread
        const searchResult = await this.tools.bhwSearch({
            sections: ['making-money'],
            min_replies: 5,
            limit: 1
        });

        if (!searchResult.success || searchResult.data.threads.length === 0) {
            console.log('No threads found for detailed content example');
            return;
        }

        const threadUrl = searchResult.data.threads[0].url;
        console.log(`Getting content from: ${threadUrl}\n`);

        const contentResult = await this.tools.bhwGetContent({
            url: threadUrl,
            include_replies: true,
            max_replies: 3,
            quality_replies_only: true
        });

        if (contentResult.success) {
            const thread = contentResult.data;
            console.log(`📋 Title: ${thread.title}`);
            console.log(`📊 Total posts: ${thread.totalPosts}`);
            console.log(`💬 Quality replies included: ${thread.filteredReplyCount}\n`);

            console.log('📝 Main Post:');
            console.log(`Author: ${thread.mainPost.author}`);
            console.log(`Content length: ${thread.mainPost.contentLength} characters`);
            console.log(`Content preview: ${this.getContentPreview(thread.mainPost.content)}\n`);

            if (thread.replies.length > 0) {
                console.log('💬 Quality Replies:');
                thread.replies.forEach((reply, index) => {
                    console.log(`${index + 1}. ${reply.author} (${reply.userTitle})`);
                    console.log(`   Content: ${this.getContentPreview(reply.content)}`);
                    console.log(`   Likes: ${reply.likes}\n`);
                });
            }
        } else {
            console.error('Failed to get content:', contentResult.error);
        }
    }

    /**
     * Example 3: Search across multiple sections for SEO content
     */
    async findSEOContent() {
        console.log('🔍 Example 3: Finding SEO content across multiple sections\n');

        const result = await this.tools.bhwSearch({
            sections: ['seo', 'white-hat-seo', 'black-hat-seo'],
            last_days: 30,
            order: 'post_date',
            direction: 'desc',
            min_replies: 3,
            limit: 10
        });

        if (result.success) {
            console.log(`Found ${result.data.threads.length} SEO threads from ${result.data.searchParams.sections.join(', ')} sections:`);
            
            // Group by section
            const threadsBySection = {};
            result.data.threads.forEach(thread => {
                if (!threadsBySection[thread.section]) {
                    threadsBySection[thread.section] = [];
                }
                threadsBySection[thread.section].push(thread);
            });

            Object.entries(threadsBySection).forEach(([section, threads]) => {
                console.log(`\n📂 ${section.toUpperCase()} (${threads.length} threads):`);
                threads.forEach(thread => {
                    console.log(`  • ${thread.title} (${thread.replies} replies)`);
                });
            });
        } else {
            console.error('SEO search failed:', result.error);
        }
    }

    /**
     * Example 4: Find trending discussions (high activity)
     */
    async findTrendingDiscussions() {
        console.log('🔥 Example 4: Finding trending discussions\n');

        const result = await this.tools.bhwSearch({
            sections: ['making-money', 'affiliate-marketing', 'social-networking'],
            last_days: 3, // Very recent
            order: 'replies',
            direction: 'desc',
            min_replies: 15, // High activity
            limit: 8
        });

        if (result.success) {
            console.log(`Found ${result.data.threads.length} trending discussions:`);
            result.data.threads.forEach((thread, index) => {
                const activityScore = thread.replies + (thread.views / 100);
                console.log(`${index + 1}. 🔥 ${thread.title}`);
                console.log(`   📊 Activity Score: ${Math.round(activityScore)} (${thread.replies} replies, ${thread.views} views)`);
                console.log(`   📂 Section: ${thread.section}`);
                console.log(`   🔗 ${thread.url}\n`);
            });
        } else {
            console.error('Trending search failed:', result.error);
        }
    }

    /**
     * Helper: Get content preview (first 150 characters)
     */
    getContentPreview(content) {
        if (!content) return 'No content';
        
        // Strip HTML tags and get plain text
        const plainText = content.replace(/<[^>]*>/g, '').trim();
        
        if (plainText.length <= 150) {
            return plainText;
        }
        
        return plainText.substring(0, 150) + '...';
    }

    /**
     * Run all examples
     */
    async runAllExamples() {
        console.log('🚀 BlackHatWorld MCP Tools - Usage Examples\n');
        console.log('===========================================\n');

        try {
            await this.findRecentMoneyMakingThreads();
            console.log('\n' + '='.repeat(50) + '\n');
            
            await this.getDetailedThreadContent();
            console.log('\n' + '='.repeat(50) + '\n');
            
            await this.findSEOContent();
            console.log('\n' + '='.repeat(50) + '\n');
            
            await this.findTrendingDiscussions();
            
            console.log('\n🎉 All examples completed successfully!');
            
        } catch (error) {
            console.error('❌ Example execution failed:', error.message);
        }
    }
}

// Run examples if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    const examples = new BHWExamples();
    examples.runAllExamples().catch(console.error);
}

export default BHWExamples;
