/**
 * BlackHatWorld Scraper Module
 * 
 * Pure scraping functionality - no business logic
 * Handles HTTP requests, retries, and basic error handling
 */

import FireCrawlApp from '@mendable/firecrawl-js';
import { FIRECRAWL_CONFIG, RATE_LIMITS, ERROR_MESSAGES } from './config.js';

class BHWScraper {
    constructor() {
        this.app = new FireCrawlApp({ apiKey: FIRECRAWL_CONFIG.apiKey });
        this.lastRequestTime = 0;
    }

    /**
     * Rate limiting helper
     */
    async waitForRateLimit() {
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        
        if (timeSinceLastRequest < RATE_LIMITS.requestDelay) {
            const waitTime = RATE_LIMITS.requestDelay - timeSinceLastRequest;
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }
        
        this.lastRequestTime = Date.now();
    }

    /**
     * Scrape a forum section page (thread list)
     * @param {string} url - Forum section URL
     * @returns {Promise<Object>} Scrape result with rawHtml
     */
    async scrapeForumPage(url) {
        await this.waitForRateLimit();
        
        try {
            console.log(`Scraping forum page: ${url}`);
            
            const result = await this.app.scrapeUrl(url, FIRECRAWL_CONFIG.defaultOptions);
            
            if (!result.success) {
                throw new Error(`${ERROR_MESSAGES.SCRAPE_FAILED}: ${result.error || 'Unknown error'}`);
            }
            
            return result;
        } catch (error) {
            console.error(`Error scraping forum page ${url}:`, error.message);
            throw error;
        }
    }

    /**
     * Scrape a thread detail page
     * @param {string} url - Thread URL
     * @returns {Promise<Object>} Scrape result with markdown and rawHtml
     */
    async scrapeThreadPage(url) {
        await this.waitForRateLimit();
        
        try {
            console.log(`Scraping thread: ${url}`);
            
            const result = await this.app.scrapeUrl(url, FIRECRAWL_CONFIG.articleOptions);
            
            if (!result.success) {
                throw new Error(`${ERROR_MESSAGES.SCRAPE_FAILED}: ${result.error || 'Unknown error'}`);
            }
            
            return result;
        } catch (error) {
            console.error(`Error scraping thread ${url}:`, error.message);
            throw error;
        }
    }

    /**
     * Scrape multiple URLs with retry logic
     * @param {string[]} urls - Array of URLs to scrape
     * @param {Function} scrapeFunction - Function to use for scraping (scrapeForumPage or scrapeThreadPage)
     * @returns {Promise<Object[]>} Array of scrape results
     */
    async scrapeMultiple(urls, scrapeFunction) {
        const results = [];
        
        for (const url of urls) {
            let retries = 0;
            let success = false;
            
            while (retries < RATE_LIMITS.maxRetries && !success) {
                try {
                    const result = await scrapeFunction.call(this, url);
                    results.push({ url, result, error: null });
                    success = true;
                } catch (error) {
                    retries++;
                    console.warn(`Retry ${retries}/${RATE_LIMITS.maxRetries} for ${url}: ${error.message}`);
                    
                    if (retries < RATE_LIMITS.maxRetries) {
                        await new Promise(resolve => setTimeout(resolve, RATE_LIMITS.retryDelay));
                    } else {
                        results.push({ url, result: null, error: error.message });
                    }
                }
            }
        }
        
        return results;
    }

    /**
     * Validate URL format
     * @param {string} url - URL to validate
     * @returns {boolean} True if valid BlackHatWorld URL
     */
    isValidBHWUrl(url) {
        try {
            const urlObj = new URL(url);
            return urlObj.hostname === 'www.blackhatworld.com';
        } catch {
            return false;
        }
    }

    /**
     * Extract thread ID from URL
     * @param {string} url - Thread URL
     * @returns {string|null} Thread ID or null if not found
     */
    extractThreadId(url) {
        const match = url.match(/\/([^\/]+)\.(\d+)\//);
        return match ? match[2] : null;
    }
}

export default BHWScraper;
