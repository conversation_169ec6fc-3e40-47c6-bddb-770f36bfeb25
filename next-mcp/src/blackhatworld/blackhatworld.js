// Legacy file - being refactored into modular structure
// This file will be replaced by: config.js, scraper.js, parser.js, mcp-tools.js

import FireCrawlApp from '@mendable/firecrawl-js';
import * as cheerio from 'cheerio';

const app = new FireCrawlApp({ apiKey: process.env.FIRECRAWL_API_KEY });

const threads = [
    "https://www.blackhatworld.com/forums/making-money.12/?last_days=7&order=post_date&direction=desc",
]

const resolveThread = async (scrapeResult) => {
    if (scrapeResult.success) {
        const $ = cheerio.load(scrapeResult.rawHtml);

        const replies = $('.structItemContainer-group .structItem:not(:has(.sticky-thread--hightlighted)) .structItem-cell--meta>dl:first-child>dd');
        const links = $(".structItemContainer-group .structItem:not(:has(.sticky-thread--hightlighted)) a[data-preview-url]");
        const hasReplyLinks = []

        replies.each((index, reply) => {
            const replyText = $(reply).text().trim();
            const replyCount = parseInt(replyText.replace(/,/g, '')) || 0; // Remove commas and parse as integer

            if (replyCount > 5 && links[index]) {
                const href = $(links[index]).attr('href');
                if (href) {
                    hasReplyLinks.push(href);
                }
            }
        });

        console.log(`Found ${hasReplyLinks.length} threads with replies > 0`);
        return hasReplyLinks;
    }
    return [];
}

const scrapeThreads = async (threads) => {
    const allValidLinks = [];

    for (const thread of threads) {
        console.log(`Scraping thread: ${thread}`);

        const scrapeResult = await app.scrapeUrl(thread, {
            formats: ["rawHtml"],
            onlyMainContent: false,
            parsePDF: false,
            maxAge: 14400000
        });

        const validLinks = await resolveThread(scrapeResult);
        allValidLinks.push(...validLinks);

        console.log(`Found ${validLinks.length} valid links from this thread`);
    }

    console.log(`Total valid links found: ${allValidLinks.length}`);
    console.log("Valid links:", allValidLinks);

    return allValidLinks;
}

const scrapeArticle = async (url) => {
    const scrapeResult = await app.scrapeUrl(url, {
        formats: ["markdown"],
        onlyMainContent: true,
        includeTags: ["[data-message-selector=\".js-post\"]"],
        parsePDF: false,
        maxAge: 14400000
    });
    console.log("scrapeResult", scrapeResult);
    return scrapeResult;
}

// scrapeThreads(threads).then(async (links) => {
//     for (const link of links) {
//         const scrapeResult = await scrapeArticle(link);
//         console.log(scrapeResult);
//         process.exit(0);
//     }
// });

scrapeArticle('https://www.blackhatworld.com/seo/amazon-associates-alternative-tier-1-traffic-500k-clicks-month.1734069/');