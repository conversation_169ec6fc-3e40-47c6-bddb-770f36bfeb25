/**
 * BlackHatWorld MCP Tools Test Suite
 * 
 * Simple functional tests to verify the tools work correctly
 * Run with: node test.js
 */

import BHWMCPTools from './mcp-tools.js';

class BHWTester {
    constructor() {
        this.tools = new BHWMCPTools();
        this.testResults = [];
    }

    /**
     * Run all tests
     */
    async runAllTests() {
        console.log('🚀 Starting BlackHatWorld MCP Tools Tests\n');

        await this.testBHWSearch();
        await this.testBHWGetContent();
        await this.testErrorHandling();

        this.printResults();
    }

    /**
     * Test bhw-search functionality
     */
    async testBHWSearch() {
        console.log('📋 Testing bhw-search...');

        try {
            // Test 1: Basic search
            const result1 = await this.tools.bhwSearch({
                sections: ['making-money'],
                last_days: 7,
                order: 'post_date',
                direction: 'desc',
                min_replies: 5,
                limit: 5
            });

            this.assert(result1.success, 'Basic search should succeed');
            this.assert(Array.isArray(result1.data.threads), 'Should return threads array');
            this.assert(result1.data.threads.length <= 5, 'Should respect limit');
            
            if (result1.data.threads.length > 0) {
                const thread = result1.data.threads[0];
                this.assert(thread.url, 'Thread should have URL');
                this.assert(thread.title, 'Thread should have title');
                this.assert(typeof thread.replies === 'number', 'Thread should have replies count');
                this.assert(thread.replies >= 5, 'Thread should meet min_replies requirement');
            }

            console.log(`✅ Found ${result1.data.threads.length} threads in making-money section`);

            // Test 2: Multiple sections
            const result2 = await this.tools.bhwSearch({
                sections: ['making-money', 'seo'],
                last_days: 30,
                limit: 3
            });

            this.assert(result2.success, 'Multi-section search should succeed');
            console.log(`✅ Multi-section search found ${result2.data.threads.length} threads`);

            // Test 3: Different sorting
            const result3 = await this.tools.bhwSearch({
                sections: ['making-money'],
                order: 'replies',
                direction: 'desc',
                limit: 3
            });

            this.assert(result3.success, 'Sort by replies should succeed');
            if (result3.data.threads.length > 1) {
                this.assert(
                    result3.data.threads[0].replies >= result3.data.threads[1].replies,
                    'Should be sorted by replies descending'
                );
            }
            console.log(`✅ Sort by replies working correctly`);

        } catch (error) {
            console.error('❌ bhw-search test failed:', error.message);
            this.testResults.push({ test: 'bhw-search', passed: false, error: error.message });
            return;
        }

        this.testResults.push({ test: 'bhw-search', passed: true });
        console.log('✅ bhw-search tests passed\n');
    }

    /**
     * Test bhw-get-content functionality
     */
    async testBHWGetContent() {
        console.log('📄 Testing bhw-get-content...');

        try {
            // First get a thread URL from search
            const searchResult = await this.tools.bhwSearch({
                sections: ['making-money'],
                limit: 1,
                min_replies: 3
            });

            if (!searchResult.success || searchResult.data.threads.length === 0) {
                console.log('⚠️  No threads found for content test, using hardcoded URL');
                // Use the URL from the original code
                var testUrl = 'https://www.blackhatworld.com/seo/amazon-associates-alternative-tier-1-traffic-500k-clicks-month.1734069/';
            } else {
                var testUrl = searchResult.data.threads[0].url;
            }

            console.log(`Testing with URL: ${testUrl}`);

            // Test 1: Get content with replies
            const result1 = await this.tools.bhwGetContent({
                url: testUrl,
                include_replies: true,
                max_replies: 5
            });

            this.assert(result1.success, 'Get content should succeed');
            this.assert(result1.data.title, 'Should have thread title');
            this.assert(result1.data.mainPost, 'Should have main post');
            this.assert(result1.data.mainPost.content, 'Main post should have content');
            this.assert(Array.isArray(result1.data.replies), 'Should have replies array');

            console.log(`✅ Retrieved thread: "${result1.data.title}"`);
            console.log(`✅ Main post length: ${result1.data.mainPost.contentLength} chars`);
            console.log(`✅ Found ${result1.data.replies.length} replies`);

            // Test 2: Get content without replies
            const result2 = await this.tools.bhwGetContent({
                url: testUrl,
                include_replies: false
            });

            this.assert(result2.success, 'Get content without replies should succeed');
            this.assert(result2.data.replies.length === 0, 'Should have no replies when include_replies=false');

            console.log(`✅ Content without replies working correctly`);

            // Test 3: Quality replies only
            const result3 = await this.tools.bhwGetContent({
                url: testUrl,
                include_replies: true,
                quality_replies_only: true,
                max_replies: 10
            });

            this.assert(result3.success, 'Quality replies filter should succeed');
            console.log(`✅ Quality filter: ${result3.data.filteredReplyCount} quality replies`);

        } catch (error) {
            console.error('❌ bhw-get-content test failed:', error.message);
            this.testResults.push({ test: 'bhw-get-content', passed: false, error: error.message });
            return;
        }

        this.testResults.push({ test: 'bhw-get-content', passed: true });
        console.log('✅ bhw-get-content tests passed\n');
    }

    /**
     * Test error handling
     */
    async testErrorHandling() {
        console.log('⚠️  Testing error handling...');

        try {
            // Test invalid section
            const result1 = await this.tools.bhwSearch({
                sections: ['invalid-section']
            });
            this.assert(!result1.success, 'Invalid section should fail');
            console.log('✅ Invalid section error handled correctly');

            // Test invalid URL
            const result2 = await this.tools.bhwGetContent({
                url: 'https://invalid-url.com/test'
            });
            this.assert(!result2.success, 'Invalid URL should fail');
            console.log('✅ Invalid URL error handled correctly');

            // Test missing URL
            const result3 = await this.tools.bhwGetContent({});
            this.assert(!result3.success, 'Missing URL should fail');
            console.log('✅ Missing URL error handled correctly');

        } catch (error) {
            console.error('❌ Error handling test failed:', error.message);
            this.testResults.push({ test: 'error-handling', passed: false, error: error.message });
            return;
        }

        this.testResults.push({ test: 'error-handling', passed: true });
        console.log('✅ Error handling tests passed\n');
    }

    /**
     * Simple assertion helper
     */
    assert(condition, message) {
        if (!condition) {
            throw new Error(`Assertion failed: ${message}`);
        }
    }

    /**
     * Print test results summary
     */
    printResults() {
        console.log('📊 Test Results Summary:');
        console.log('========================');

        let passed = 0;
        let failed = 0;

        this.testResults.forEach(result => {
            if (result.passed) {
                console.log(`✅ ${result.test}: PASSED`);
                passed++;
            } else {
                console.log(`❌ ${result.test}: FAILED - ${result.error}`);
                failed++;
            }
        });

        console.log(`\nTotal: ${passed + failed} tests, ${passed} passed, ${failed} failed`);

        if (failed === 0) {
            console.log('🎉 All tests passed!');
        } else {
            console.log('💥 Some tests failed. Check the errors above.');
        }
    }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    const tester = new BHWTester();
    tester.runAllTests().catch(console.error);
}

export default BHWTester;
