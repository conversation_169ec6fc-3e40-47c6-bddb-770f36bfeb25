/**
 * BlackHatWorld MCP Tools
 * 
 * Main MCP tool interfaces following the simplified design
 * Two core tools: bhw-search and bhw-get-content
 */

import BHWScraper from './scraper.js';
import BHWParser from './parser.js';
import { buildForumUrl, BHW_SECTIONS, SEARCH_OPTIONS, ERROR_MESSAGES } from './config.js';

class BHWMCPTools {
    constructor() {
        this.scraper = new BHWScraper();
        this.parser = new BHWParser();
    }

    /**
     * MCP Tool: bhw-search
     * Search BlackHatWorld forums for threads matching criteria
     *
     * @param {Object} params - Search parameters
     * @param {string} params.query - Search keywords (optional)
     * @param {string[]} params.sections - Forum sections to search
     * @param {number} params.last_days - Days to look back (1, 7, 30, 90, 365) - only for forum browsing
     * @param {string} params.order - Sort order (post_date, replies, views) for forum browsing, (relevance, date, replies) for keyword search
     * @param {string} params.direction - Sort direction (desc, asc) - only for forum browsing
     * @param {number} params.min_replies - Minimum reply count
     * @param {number} params.limit - Maximum results to return
     * @returns {Promise<Object>} Search results
     */
    async bhwSearch(params = {}) {
        try {
            // Validate and set defaults
            const {
                query = '',
                sections = ['making-money'],
                last_days = 7,
                direction = 'desc',
                min_replies = 0,
                limit = 20
            } = params;

            // Determine search mode: keyword search vs forum browsing
            const isKeywordSearch = query && query.trim().length > 0;

            // Set appropriate default order based on search mode
            const order = params.order || (isKeywordSearch ? 'relevance' : 'post_date');

            // Validate parameters
            this.validateSearchParams({
                query, sections, last_days, order, direction, min_replies, limit, isKeywordSearch
            });

            if (isKeywordSearch) {
                console.log(`Starting BHW keyword search: query="${query}", sections=${sections.join(',')}, order=${order}`);
            } else {
                console.log(`Starting BHW forum browsing: sections=${sections.join(',')}, last_days=${last_days}, order=${order}`);
            }

            const allThreads = [];

            if (isKeywordSearch) {
                // Keyword search mode - browse forums and filter by keywords
                console.log(`Performing keyword-based filtering for: "${query}"`);

                // Browse all sections and collect threads
                for (const section of sections) {
                    try {
                        // Use a longer time period for keyword search to get more results
                        const searchDays = Math.max(last_days, 30); // At least 30 days for keyword search
                        const url = buildForumUrl(section, {
                            last_days: searchDays,
                            order: 'post_date',
                            direction: 'desc'
                        });

                        const scrapeResult = await this.scraper.scrapeForumPage(url);

                        if (scrapeResult.success && scrapeResult.rawHtml) {
                            const threads = this.parser.parseForumPage(scrapeResult.rawHtml);

                            // Add section info to each thread
                            threads.forEach(thread => {
                                thread.section = section;
                            });

                            allThreads.push(...threads);
                            console.log(`Found ${threads.length} threads in ${section} for keyword filtering`);
                        }
                    } catch (error) {
                        console.error(`Error searching section ${section}:`, error.message);
                        // Continue with other sections
                    }
                }
            } else {
                // Forum browsing mode - use forum URLs
                for (const section of sections) {
                    try {
                        const url = buildForumUrl(section, { last_days, order, direction });
                        const scrapeResult = await this.scraper.scrapeForumPage(url);

                        if (scrapeResult.success && scrapeResult.rawHtml) {
                            const threads = this.parser.parseForumPage(scrapeResult.rawHtml);

                            // Add section info to each thread
                            threads.forEach(thread => {
                                thread.section = section;
                            });

                            allThreads.push(...threads);
                            console.log(`Found ${threads.length} threads in ${section}`);
                        }
                    } catch (error) {
                        console.error(`Error searching section ${section}:`, error.message);
                        // Continue with other sections
                    }
                }
            }

            // Filter by minimum replies and keywords
            let filteredThreads = allThreads.filter(thread => thread.replies >= min_replies);

            // Apply keyword filtering if in keyword search mode
            if (isKeywordSearch) {
                const keywords = query.toLowerCase().split(/\s+/).filter(k => k.length > 0);
                console.log(`Filtering by keywords: ${keywords.join(', ')}`);

                filteredThreads = filteredThreads.filter(thread => {
                    const searchText = (thread.title + ' ' + (thread.snippet || '')).toLowerCase();

                    // Check if any keyword matches (OR logic)
                    return keywords.some(keyword => searchText.includes(keyword));
                });

                console.log(`After keyword filtering: ${filteredThreads.length} threads`);
            }

            // Sort results
            filteredThreads.sort((a, b) => {
                if (isKeywordSearch && order === 'relevance') {
                    // For keyword search, calculate relevance score
                    const keywords = query.toLowerCase().split(/\s+/).filter(k => k.length > 0);

                    const scoreA = this.calculateRelevanceScore(a, keywords);
                    const scoreB = this.calculateRelevanceScore(b, keywords);

                    return scoreB - scoreA; // Higher score first
                } else if (order === 'replies') {
                    return direction === 'desc' ? b.replies - a.replies : a.replies - b.replies;
                } else if (order === 'views') {
                    return direction === 'desc' ? b.views - a.views : a.views - b.views;
                } else { // post_date or date
                    const dateA = new Date(a.lastActivity || 0);
                    const dateB = new Date(b.lastActivity || 0);
                    return direction === 'desc' ? dateB - dateA : dateA - dateB;
                }
            });

            // Limit results
            const limitedThreads = filteredThreads.slice(0, limit);

            return {
                success: true,
                data: {
                    threads: limitedThreads,
                    totalFound: allThreads.length,
                    filteredCount: filteredThreads.length,
                    returnedCount: limitedThreads.length,
                    searchParams: {
                        query: query || '',
                        isKeywordSearch,
                        sections,
                        last_days,
                        order,
                        direction,
                        min_replies,
                        limit
                    },
                    searchedAt: new Date().toISOString()
                }
            };

        } catch (error) {
            console.error('BHW Search error:', error.message);
            return {
                success: false,
                error: error.message,
                data: null
            };
        }
    }

    /**
     * MCP Tool: bhw-get-content
     * Get detailed content from a specific BlackHatWorld thread
     * 
     * @param {Object} params - Content retrieval parameters
     * @param {string} params.url - Thread URL
     * @param {boolean} params.include_replies - Include reply posts
     * @param {number} params.max_replies - Maximum replies to return
     * @param {boolean} params.quality_replies_only - Filter for quality replies
     * @returns {Promise<Object>} Thread content
     */
    async bhwGetContent(params = {}) {
        try {
            const {
                url,
                include_replies = true,
                max_replies = 50,
                quality_replies_only = false
            } = params;

            // Validate URL
            if (!url) {
                throw new Error('URL parameter is required');
            }

            if (!this.scraper.isValidBHWUrl(url)) {
                throw new Error(`${ERROR_MESSAGES.INVALID_URL}: ${url}`);
            }

            console.log(`Getting content from: ${url}`);

            // Scrape the thread page
            const scrapeResult = await this.scraper.scrapeThreadPage(url);

            if (!scrapeResult.success) {
                throw new Error(`Failed to scrape thread: ${scrapeResult.error}`);
            }

            // Parse the content
            const threadData = this.parser.parseThreadPage(
                scrapeResult.rawHtml,
                scrapeResult.markdown
            );

            // Set the URL
            threadData.url = url;
            threadData.threadId = this.scraper.extractThreadId(url);

            // Filter replies if requested
            if (include_replies && threadData.replies) {
                let replies = threadData.replies;

                // Filter for quality if requested
                if (quality_replies_only) {
                    replies = replies.filter(reply =>
                        reply.contentLength > 100 || // Substantial content
                        reply.likes > 0 ||           // Has likes
                        reply.userTitle.includes('VIP') || // VIP user
                        reply.userTitle.includes('Jr. VIP') // Jr VIP user
                    );
                }

                // Limit number of replies
                if (max_replies > 0) {
                    replies = replies.slice(0, max_replies);
                }

                threadData.replies = replies;
                threadData.filteredReplyCount = replies.length;
            } else if (!include_replies) {
                threadData.replies = [];
                threadData.filteredReplyCount = 0;
            }

            return {
                success: true,
                data: threadData
            };

        } catch (error) {
            console.error('BHW Get Content error:', error.message);
            return {
                success: false,
                error: error.message,
                data: null
            };
        }
    }

    /**
     * Validate search parameters
     * @private
     */
    validateSearchParams({ query, sections, last_days, order, direction, min_replies, limit, isKeywordSearch }) {
        // Validate sections
        if (!Array.isArray(sections) || sections.length === 0) {
            throw new Error('Sections must be a non-empty array');
        }

        for (const section of sections) {
            if (!BHW_SECTIONS[section]) {
                throw new Error(`Invalid section: ${section}. Available: ${Object.keys(BHW_SECTIONS).join(', ')}`);
            }
        }

        // Validate keyword search specific parameters
        if (isKeywordSearch) {
            if (typeof query !== 'string' || query.trim().length === 0) {
                throw new Error('Query must be a non-empty string for keyword search');
            }

            if (query.length > SEARCH_OPTIONS.maxKeywordLength) {
                throw new Error(`Query too long. Maximum length: ${SEARCH_OPTIONS.maxKeywordLength} characters`);
            }

            // For keyword search, validate search-specific orders
            if (!SEARCH_OPTIONS.searchOrders.includes(order)) {
                throw new Error(`Invalid order for keyword search: ${order}. Available: ${SEARCH_OPTIONS.searchOrders.join(', ')}`);
            }
        } else {
            // For forum browsing, validate forum-specific parameters
            if (!SEARCH_OPTIONS.timePeriods.includes(last_days)) {
                throw new Error(`Invalid last_days: ${last_days}. Available: ${SEARCH_OPTIONS.timePeriods.join(', ')}`);
            }

            if (!SEARCH_OPTIONS.orders.includes(order)) {
                throw new Error(`Invalid order: ${order}. Available: ${SEARCH_OPTIONS.orders.join(', ')}`);
            }

            if (!SEARCH_OPTIONS.directions.includes(direction)) {
                throw new Error(`Invalid direction: ${direction}. Available: ${SEARCH_OPTIONS.directions.join(', ')}`);
            }
        }

        // Common validations for both modes
        if (typeof min_replies !== 'number' || min_replies < 0) {
            throw new Error('min_replies must be a non-negative number');
        }

        if (typeof limit !== 'number' || limit < 1 || limit > SEARCH_OPTIONS.maxResults) {
            throw new Error(`limit must be between 1 and ${SEARCH_OPTIONS.maxResults}`);
        }
    }

    /**
     * Calculate relevance score for keyword search
     * @private
     */
    calculateRelevanceScore(thread, keywords) {
        let score = 0;
        const title = thread.title.toLowerCase();
        const snippet = (thread.snippet || '').toLowerCase();

        keywords.forEach(keyword => {
            // Title matches are worth more
            if (title.includes(keyword)) {
                score += 10;

                // Exact word matches in title are worth even more
                if (title.split(/\s+/).includes(keyword)) {
                    score += 5;
                }
            }

            // Snippet matches
            if (snippet.includes(keyword)) {
                score += 3;
            }
        });

        // Boost score based on thread activity (replies and views)
        score += Math.min(thread.replies * 0.1, 5); // Max 5 points from replies
        score += Math.min(thread.views * 0.001, 3); // Max 3 points from views

        return score;
    }
}

export default BHWMCPTools;
