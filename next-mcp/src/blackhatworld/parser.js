/**
 * BlackHatWorld Parser Module
 * 
 * HTML parsing and data extraction logic
 * Converts raw HTML into structured data objects
 */

import * as cheerio from 'cheerio';
import { SELECTORS, BHW_BASE_URL, ERROR_MESSAGES } from './config.js';

class BHWParser {
    /**
     * Parse forum page HTML to extract thread list
     * @param {string} html - Raw HTML from forum page
     * @returns {Object[]} Array of thread objects
     */
    parseForumPage(html) {
        try {
            const $ = cheerio.load(html);
            const threads = [];

            $(SELECTORS.threadContainer).each((index, element) => {
                const $thread = $(element);

                // Extract basic thread info
                const linkElement = $thread.find(SELECTORS.threadLink).first();
                const titleElement = $thread.find(SELECTORS.threadTitle).first();

                if (!linkElement.length || !titleElement.length) {
                    return; // Skip invalid threads
                }

                const relativeUrl = linkElement.attr('href');
                const url = relativeUrl ? this.normalizeUrl(relativeUrl) : null;
                const title = titleElement.text().trim();

                if (!url || !title) {
                    return; // Skip threads without URL or title
                }

                // Extract metadata
                const repliesText = $thread.find(SELECTORS.threadReplies).text().trim();
                const viewsText = $thread.find(SELECTORS.threadViews).text().trim();
                const author = $thread.find(SELECTORS.threadAuthor).text().trim();
                const dateElement = $thread.find(SELECTORS.threadDate);

                const thread = {
                    url,
                    title,
                    author,
                    replies: this.parseNumber(repliesText),
                    views: this.parseNumber(viewsText),
                    lastActivity: this.parseDate(dateElement),
                    threadId: this.extractThreadIdFromUrl(url)
                };

                threads.push(thread);
            });

            console.log(`Parsed ${threads.length} threads from forum page`);
            return threads;

        } catch (error) {
            console.error('Error parsing forum page:', error.message);
            throw new Error(`${ERROR_MESSAGES.PARSE_FAILED}: ${error.message}`);
        }
    }

    /**
     * Parse thread page HTML to extract post content and replies
     * @param {string} html - Raw HTML from thread page
     * @param {string} markdown - Markdown content from FireCrawl
     * @returns {Object} Thread object with posts
     */
    parseThreadPage(html, markdown) {
        try {
            const $ = cheerio.load(html);
            const posts = [];

            // Extract all posts (main post + replies)
            $(SELECTORS.replyContainer).each((index, element) => {
                const $post = $(element);

                const author = $post.find(SELECTORS.postAuthor).text().trim();
                const content = $post.find(SELECTORS.postContent).html() || '';
                const dateElement = $post.find(SELECTORS.postDate);
                const postNumber = $post.find(SELECTORS.postNumber).text().trim();

                // Extract user info for quality assessment
                const userTitle = $post.find(SELECTORS.userTitle).text().trim();
                const likeCount = this.parseNumber($post.find(SELECTORS.likeCount).text());

                const post = {
                    postNumber: this.parseNumber(postNumber) || index + 1,
                    author,
                    userTitle,
                    content: this.cleanContent(content),
                    contentLength: content.replace(/<[^>]*>/g, '').length,
                    date: this.parseDate(dateElement),
                    likes: likeCount,
                    isMainPost: index === 0
                };

                posts.push(post);
            });

            // Extract thread metadata
            const title = $('h1.p-title-value').text().trim() ||
                $('.titleBar h1').text().trim() ||
                'Unknown Title';

            const result = {
                title,
                url: '', // Will be set by caller
                posts,
                totalPosts: posts.length,
                mainPost: posts[0] || null,
                replies: posts.slice(1),
                markdown: markdown || '',
                extractedAt: new Date().toISOString()
            };

            console.log(`Parsed thread with ${posts.length} posts`);
            return result;

        } catch (error) {
            console.error('Error parsing thread page:', error.message);
            throw new Error(`${ERROR_MESSAGES.PARSE_FAILED}: ${error.message}`);
        }
    }

    /**
     * Clean HTML content for better readability
     * @param {string} html - Raw HTML content
     * @returns {string} Cleaned HTML
     */
    cleanContent(html) {
        if (!html) return '';

        // Remove quotes, signatures, and other noise
        let cleaned = html
            .replace(/<div class="bbCodeBlock.*?<\/div>/gs, '') // Remove quote blocks
            .replace(/<div class="message-signature.*?<\/div>/gs, '') // Remove signatures
            .replace(/<script.*?<\/script>/gs, '') // Remove scripts
            .replace(/\s+/g, ' ') // Normalize whitespace
            .trim();

        return cleaned;
    }

    /**
     * Parse number from text (handles commas, K, M suffixes)
     * @param {string} text - Text containing number
     * @returns {number} Parsed number or 0
     */
    parseNumber(text) {
        if (!text) return 0;

        const cleaned = text.replace(/,/g, '').toLowerCase();
        const match = cleaned.match(/(\d+(?:\.\d+)?)\s*([km]?)/);

        if (!match) return 0;

        let num = parseFloat(match[1]);
        const suffix = match[2];

        if (suffix === 'k') num *= 1000;
        if (suffix === 'm') num *= 1000000;

        return Math.floor(num);
    }

    /**
     * Parse date from element or text
     * @param {Object} element - Cheerio element or text
     * @returns {string|null} ISO date string or null
     */
    parseDate(element) {
        if (!element) return null;

        // Try to get datetime attribute first
        const datetime = element.attr ? element.attr('datetime') : null;
        if (datetime) {
            return new Date(datetime).toISOString();
        }

        // Try to parse text content
        const text = element.text ? element.text().trim() : element.toString();
        if (text) {
            const date = new Date(text);
            if (!isNaN(date.getTime())) {
                return date.toISOString();
            }
        }

        return null;
    }

    /**
     * Normalize relative URLs to absolute URLs
     * @param {string} url - Relative or absolute URL
     * @returns {string} Absolute URL
     */
    normalizeUrl(url) {
        if (url.startsWith('http')) {
            return url;
        }
        return BHW_BASE_URL + (url.startsWith('/') ? url : '/' + url);
    }

    /**
     * Parse search results page HTML to extract thread list
     * @param {string} html - Raw HTML from search results page
     * @returns {Object[]} Array of thread objects
     */
    parseSearchPage(html) {
        try {
            const $ = cheerio.load(html);
            const threads = [];

            // Search results use different selectors
            $('.contentRow-main').each((index, element) => {
                const $result = $(element);

                // Extract title and URL
                const titleElement = $result.find('.contentRow-title a').first();
                if (!titleElement.length) {
                    return; // Skip invalid results
                }

                const title = titleElement.text().trim();
                const relativeUrl = titleElement.attr('href');
                const url = relativeUrl ? this.normalizeUrl(relativeUrl) : null;

                if (!url || !title) {
                    return; // Skip results without URL or title
                }

                // Extract metadata from search results
                const metaElement = $result.find('.contentRow-lesser');
                const authorElement = $result.find('.username').first();
                const dateElement = $result.find('time').first();

                // Extract section info from breadcrumb or category
                const categoryElement = $result.find('.contentRow-minor a').first();
                const section = categoryElement.length ?
                    this.extractSectionFromUrl(categoryElement.attr('href')) : 'unknown';

                // Extract snippet/preview
                const snippetElement = $result.find('.contentRow-snippet');
                const snippet = snippetElement.length ?
                    snippetElement.text().trim() : '';

                const thread = {
                    url,
                    title,
                    author: authorElement.length ? authorElement.text().trim() : 'Unknown',
                    section,
                    snippet,
                    lastActivity: this.parseDate(dateElement),
                    threadId: this.extractThreadIdFromUrl(url),
                    // Search results don't always have reply/view counts
                    replies: 0,
                    views: 0,
                    isSearchResult: true
                };

                threads.push(thread);
            });

            console.log(`Parsed ${threads.length} threads from search results`);
            return threads;

        } catch (error) {
            console.error('Error parsing search results page:', error.message);
            throw new Error(`${ERROR_MESSAGES.PARSE_FAILED}: ${error.message}`);
        }
    }

    /**
     * Extract section name from forum URL
     * @param {string} url - Forum URL
     * @returns {string} Section name
     */
    extractSectionFromUrl(url) {
        if (!url) return 'unknown';

        const match = url.match(/\/forums\/([^.]+)\./);
        return match ? match[1] : 'unknown';
    }

    /**
     * Extract thread ID from URL
     * @param {string} url - Thread URL
     * @returns {string|null} Thread ID
     */
    extractThreadIdFromUrl(url) {
        const match = url.match(/\.(\d+)\//);
        return match ? match[1] : null;
    }
}

export default BHWParser;
