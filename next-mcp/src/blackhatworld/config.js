/**
 * BlackHatWorld MCP Tools Configuration
 * 
 * Centralized configuration for all constants, selectors, and settings
 * Following KISS principle - simple constants that can be easily updated
 */

// API Configuration
export const FIRECRAWL_CONFIG = {
    apiKey: process.env.FIRECRAWL_API_KEY || "fc-cf9053137f414397a443f82e52aafe89",
    defaultOptions: {
        formats: ["rawHtml"],
        onlyMainContent: false,
        parsePDF: false,
        maxAge: 14400000 // 4 hours cache
    },
    articleOptions: {
        formats: ["markdown", "rawHtml"],
        onlyMainContent: true,
        parsePDF: false,
        maxAge: 14400000
    }
};

// BlackHatWorld Forum Sections
export const BHW_SECTIONS = {
    'making-money': 12,
    'seo': 4,
    'affiliate-marketing': 39,
    'social-networking': 24,
    'blogging': 132,
    'black-hat-seo': 3,
    'white-hat-seo': 4,
    'ppc': 40,
    'copywriting': 142,
    'programming': 65,
    'design': 66,
    'marketplace': 20
};

// URL Building
export const BHW_BASE_URL = "https://www.blackhatworld.com";

export const buildForumUrl = (section, options = {}) => {
    const {
        last_days = 7,
        order = 'post_date',
        direction = 'desc',
        page = 1
    } = options;

    const sectionId = BHW_SECTIONS[section];
    if (!sectionId) {
        throw new Error(`Unknown section: ${section}. Available: ${Object.keys(BHW_SECTIONS).join(', ')}`);
    }

    return `${BHW_BASE_URL}/forums/${section}.${sectionId}/?last_days=${last_days}&order=${order}&direction=${direction}&page=${page}`;
};

// Build search URL with keyword support
// Note: BlackHatWorld search requires a specific URL format
export const buildSearchUrl = (options = {}) => {
    const {
        query = '',
        sections = [],
        order = 'relevance',
        type = 'post',
        page = 1
    } = options;

    // For now, we'll use a simpler approach that works with BHW's search system
    // Base search URL with a generic search ID (we'll use a common one)
    let searchUrl = `${BHW_BASE_URL}/search/member`;

    // Add search parameters
    const params = new URLSearchParams();

    // Add keyword query
    if (query.trim()) {
        params.append('q', query.trim());
    }

    // Add order/sorting
    params.append('o', order);

    // Add search type
    if (type) {
        params.append('t', type);
    }

    // Add page
    if (page > 1) {
        params.append('page', page);
    }

    // Add section filtering if specified
    if (sections.length > 0) {
        // Enable child nodes for section filtering
        params.append('c[child_nodes]', '1');

        // Add each section ID
        sections.forEach(section => {
            const sectionId = BHW_SECTIONS[section];
            if (sectionId) {
                params.append('c[nodes][]', sectionId);
            }
        });
    }

    return searchUrl + '?' + params.toString();
};

// CSS Selectors (centralized for easy updates when site changes)
export const SELECTORS = {
    // Thread list page selectors
    threadContainer: '.structItemContainer-group .structItem:not(:has(.sticky-thread--highlighted))',
    threadLink: 'a[data-preview-url]',
    threadTitle: '.structItem-title a',
    threadReplies: '.structItem-cell--meta > dl:first-child > dd',
    threadViews: '.structItem-cell--meta > dl:nth-child(2) > dd',
    threadAuthor: '.structItem-cell--main .structItem-minor .username',
    threadDate: '.structItem-cell--latest time',

    // Thread detail page selectors
    postContainer: '[data-message-selector=".js-post"]',
    postContent: '.bbWrapper',
    postAuthor: '.message-name .username',
    postDate: '.message-date time',
    postNumber: '.message-number',
    replyContainer: '.message--post',

    // Quality indicators
    likeCount: '.reactionsBar-item--like .reactionsBar-text',
    userTitle: '.userTitle',
    userMessages: '.pairs.pairs--justified dd'
};

// Search and Filter Options
export const SEARCH_OPTIONS = {
    orders: ['post_date', 'replies', 'views', 'title'],
    directions: ['desc', 'asc'],
    timePeriods: [1, 3, 7, 30, 90, 365, 'all'], // Added 3 days option
    minReplies: 0,
    maxResults: 50,
    // Keyword search specific options
    searchOrders: ['relevance', 'date', 'replies'],
    searchTypes: ['post', 'thread'],
    maxKeywordLength: 100
};

// Quality Thresholds (simple initial values)
export const QUALITY_THRESHOLDS = {
    minReplies: 5,
    minViews: 100,
    minContentLength: 200,
    maxAge: 365 // days
};

// Error Messages
export const ERROR_MESSAGES = {
    INVALID_SECTION: 'Invalid forum section',
    SCRAPE_FAILED: 'Failed to scrape content',
    PARSE_FAILED: 'Failed to parse HTML',
    INVALID_URL: 'Invalid thread URL',
    RATE_LIMITED: 'Rate limited by server',
    NETWORK_ERROR: 'Network connection error'
};

// Rate Limiting
export const RATE_LIMITS = {
    requestDelay: 3000, // 3 seconds between requests (increased to avoid 429)
    maxRetries: 3,
    retryDelay: 5000 // 5 seconds between retries (increased)
};
