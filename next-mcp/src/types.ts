/**
 * Type definitions for the MCP server
 */

import { z } from "zod";

// ============================================================================
// TOOL SCHEMAS
// ============================================================================

export const CalculatorInputSchema = z.object({
  operation: z.enum(["add", "subtract", "multiply", "divide"]),
  a: z.number().describe("First number"),
  b: z.number().describe("Second number")
});

export const TextTransformInputSchema = z.object({
  text: z.string().describe("Text to transform"),
  transform: z.enum(["uppercase", "lowercase", "reverse", "word-count"]).describe("Type of transformation")
});

export const CurrentTimeInputSchema = z.object({
  format: z.enum(["iso", "locale", "timestamp"]).optional().describe("Time format (default: iso)")
});

// ============================================================================
// PROMPT SCHEMAS
// ============================================================================

export const CodeReviewArgsSchema = z.object({
  code: z.string().describe("Code to review"),
  language: z.string().optional().describe("Programming language (optional)")
});

export const ExplainConceptArgsSchema = z.object({
  concept: z.string().describe("Concept to explain"),
  audience: z.enum(["beginner", "intermediate", "advanced"]).describe("Target audience level"),
  includeExamples: z.boolean().optional().describe("Include examples in explanation")
});

// ============================================================================
// RESOURCE TYPES
// ============================================================================

export interface ServerInfo {
  name: string;
  version: string;
  description: string;
  capabilities: string[];
  timestamp: string;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type CalculatorInput = z.infer<typeof CalculatorInputSchema>;
export type TextTransformInput = z.infer<typeof TextTransformInputSchema>;
export type CurrentTimeInput = z.infer<typeof CurrentTimeInputSchema>;
export type CodeReviewArgs = z.infer<typeof CodeReviewArgsSchema>;
export type ExplainConceptArgs = z.infer<typeof ExplainConceptArgsSchema>;

// ============================================================================
// ERROR TYPES
// ============================================================================

export class MCPServerError extends Error {
  constructor(
    message: string,
    public code: number = -32000,
    public data?: unknown
  ) {
    super(message);
    this.name = "MCPServerError";
  }
}

export class ValidationError extends MCPServerError {
  constructor(message: string, data?: unknown) {
    super(message, -32602, data);
    this.name = "ValidationError";
  }
}

export class NotFoundError extends MCPServerError {
  constructor(resource: string) {
    super(`Resource not found: ${resource}`, -32601);
    this.name = "NotFoundError";
  }
}

// ============================================================================
// X/TWITTER SEARCH TYPES
// ============================================================================

export interface XSearchOptions {
  query: string;
  queryType?: 'Latest' | 'Top';
  maxResults?: number;
  cursor?: string;
}

export interface XTweetMetrics {
  retweetCount: number;
  replyCount: number;
  likeCount: number;
  quoteCount: number;
  viewCount?: number;
  bookmarkCount?: number;
}

export interface XTweetEntities {
  hashtags?: Array<{
    indices: number[];
    text: string;
  }>;
  urls?: Array<{
    display_url: string;
    expanded_url: string;
    indices: number[];
    url: string;
  }>;
  user_mentions?: Array<{
    id_str: string;
    name: string;
    screen_name: string;
  }>;
}

export interface XUser {
  type: 'user';
  userName: string;
  url: string;
  id: string;
  name: string;
  isBlueVerified: boolean;
  verifiedType?: string;
  profilePicture?: string;
  coverPicture?: string;
  description?: string;
  location?: string;
  followers: number;
  following: number;
  canDm: boolean;
  createdAt: string;
  favouritesCount: number;
  hasCustomTimelines: boolean;
  isTranslator: boolean;
  mediaCount: number;
  statusesCount: number;
  withheldInCountries?: string[];
  possiblySensitive?: boolean;
  pinnedTweetIds?: string[];
  isAutomated?: boolean;
  automatedBy?: string;
  unavailable?: boolean;
  message?: string;
  unavailableReason?: string;
}

export interface XTweet {
  type: 'tweet';
  id: string;
  url: string;
  text: string;
  source?: string;
  retweetCount: number;
  replyCount: number;
  likeCount: number;
  quoteCount: number;
  viewCount?: number;
  createdAt: string;
  lang?: string;
  bookmarkCount?: number;
  isReply: boolean;
  inReplyToId?: string;
  conversationId?: string;
  inReplyToUserId?: string;
  inReplyToUsername?: string;
  author: XUser;
  entities?: XTweetEntities;
  quoted_tweet?: any;
  retweeted_tweet?: any;
}

export interface XSearchResponse {
  tweets: XTweet[];
  has_next_page: boolean;
  next_cursor?: string;
}

export interface XTopicAnalysis {
  topic: string;
  totalTweets: number;
  timeRange: string;
  sentiment: 'positive' | 'negative' | 'neutral' | 'mixed';
  topUsers: XUser[];
  popularTweets: XTweet[];
  trends: string[];
  insights: string[];
  engagementStats: {
    avgLikes: number;
    avgRetweets: number;
    avgReplies: number;
    totalEngagement: number;
  };
}

export interface XUserInsights {
  user: XUser;
  recentTweets: XTweet[];
  activityStats: {
    tweetsPerDay: number;
    avgEngagement: number;
    topHashtags: string[];
    mentionedUsers: string[];
  };
  influence: {
    score: number;
    category: 'low' | 'medium' | 'high' | 'very_high';
    factors: string[];
  };
}
