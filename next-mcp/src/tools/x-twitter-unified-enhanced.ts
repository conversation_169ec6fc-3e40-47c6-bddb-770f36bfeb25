/**
 * Enhanced Unified X/Twitter API Tool with Integrated Query Builder
 * Provides access to any TwitterAPI.io endpoint with built-in query optimization
 */

import { z } from "zod";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { logger } from "../utils.js";
import {
    XTwitterQueryBuilder,
    INDUSTRY_KEYWORDS,
    EXCLUSION_PATTERNS,
    QUALITY_PRESETS,
    QUERY_TEMPLATES
} from "./x-twitter-query-builder.js";

// Available TwitterAPI.io endpoints with their descriptions
export const AVAILABLE_ENDPOINTS = {
    // User endpoints
    "user/batch_get_user_by_userids": {
        method: "GET",
        description: "Get multiple users by their user IDs",
        exampleParams: { userIds: ["123456", "789012"] }
    },
    "user/get_user_by_username": {
        method: "GET",
        description: "Get user information by username",
        exampleParams: { username: "elonmusk" }
    },
    "user/get_user_last_tweets": {
        method: "GET",
        description: "Get a user's most recent tweets",
        exampleParams: { username: "elonmusk", count: 20 }
    },
    "user/get_user_followings": {
        method: "GET",
        description: "Get list of users that a user follows",
        exampleParams: { username: "elonmusk", cursor: null }
    },
    "user/get_user_followers": {
        method: "GET",
        description: "Get list of users that follow a user",
        exampleParams: { username: "elonmusk", cursor: null }
    },
    "user/get_user_mention": {
        method: "GET",
        description: "Get user mentions",
        exampleParams: { username: "elonmusk", cursor: null }
    },
    "user/search_user": {
        method: "GET",
        description: "Search user by keyword",
        exampleParams: { query: "elon", cursor: null }
    },

    // Tweet endpoints
    "tweet/get_tweet_by_ids": {
        method: "GET",
        description: "Get tweets by their IDs",
        exampleParams: { tweetIds: ["1234567890", "0987654321"] }
    },
    "tweet/get_tweet_reply": {
        method: "GET",
        description: "Get replies to a specific tweet",
        exampleParams: { tweetId: "1234567890", cursor: null }
    },
    "tweet/get_tweet_quote": {
        method: "GET",
        description: "Get tweet quotations",
        exampleParams: { tweetId: "1234567890", cursor: null }
    },
    "tweet/get_tweet_retweeter": {
        method: "GET",
        description: "Get tweet retweeters",
        exampleParams: { tweetId: "1234567890", cursor: null }
    },
    "tweet/get_tweet_thread_context": {
        method: "GET",
        description: "Get the full thread context of a tweet",
        exampleParams: { tweetId: "1234567890" }
    },
    "tweet/get_article": {
        method: "GET",
        description: "Get article content from a tweet",
        exampleParams: { tweetId: "1234567890" }
    },
    "tweet/advanced_search": {
        method: "GET",
        description: "Advanced tweet search with query syntax. Supports industry keywords, qualifiers, and exclusions for precise results.",
        exampleParams: {
            query: "from:elonmusk min_likes:1000",
            queryType: "Latest",
            cursor: null
        },
        queryGuidance: {
            industryKeywords: INDUSTRY_KEYWORDS,
            qualifiers: {
                engagement: "min_faves:50 min_retweets:10 min_replies:5",
                quality: "-is:retweet -is:reply lang:en",
                timeRange: "since:2024-01-01 until:2024-12-31",
                media: "has:media has:images has:video",
                links: "has:links -has:links",
                verified: "from:verified"
            },
            exclusions: EXCLUSION_PATTERNS,
            examples: [
                'SEO "best practices" min_faves:100 -is:retweet lang:en',
                'SaaS growth OR "growth hacking" from:verified min_retweets:50',
                '(AI OR "artificial intelligence") AND marketing -"crypto" -"NFT"',
                '"product led growth" OR PLG min_faves:20 has:links'
            ]
        }
    },

    // List endpoints
    "list/get_list_tweet": {
        method: "GET",
        description: "Get tweets from a specific list",
        exampleParams: { listId: "1234567890", cursor: null }
    },
    "list/get_list_followers": {
        method: "GET",
        description: "Get list followers",
        exampleParams: { listId: "1234567890", cursor: null }
    },
    "list/get_list_members": {
        method: "GET",
        description: "Get list members",
        exampleParams: { listId: "1234567890", cursor: null }
    },

    // Community endpoints
    "community/get_community_by_id": {
        method: "GET",
        description: "Get community info by ID",
        exampleParams: { communityId: "1234567890" }
    },
    "community/get_community_members": {
        method: "GET",
        description: "Get community members",
        exampleParams: { communityId: "1234567890", cursor: null }
    },
    "community/get_community_moderators": {
        method: "GET",
        description: "Get community moderators",
        exampleParams: { communityId: "1234567890", cursor: null }
    },
    "community/get_community_tweets": {
        method: "GET",
        description: "Get tweets from a specific community",
        exampleParams: { communityId: "1234567890", cursor: null }
    },

    // Trend endpoints
    "trend/get_trends": {
        method: "GET",
        description: "Get trending topics",
        exampleParams: { location: "worldwide" }
    }
} as const;

type EndpointName = keyof typeof AVAILABLE_ENDPOINTS;

export class XTwitterUnifiedClient {
    private readonly apiKey: string;
    private readonly baseUrl = "https://api.twitterapi.io/twitter";
    private readonly defaultTimeout = 30000;

    constructor() {
        this.apiKey = process.env["X_API_KEY"] || "";
        if (!this.apiKey) {
            throw new Error(
                "X_API_KEY environment variable is required. " +
                "Please set it in your MCP configuration file under the 'env' field."
            );
        }
    }

    /**
     * Make a request to any TwitterAPI.io endpoint
     */
    async request(
        endpoint: string,
        method: string = "GET",
        params: Record<string, any> = {}
    ): Promise<any> {
        // Construct the full URL
        const url = endpoint.startsWith("http")
            ? endpoint
            : `${this.baseUrl}/${endpoint.replace(/^\//, "")}`;

        logger.info(`Making ${method} request to: ${url}`);
        logger.debug(`Parameters:`, params);

        try {
            let fetchUrl = url;
            let fetchOptions: RequestInit = {
                method,
                headers: {
                    'X-API-Key': this.apiKey,
                    'Content-Type': 'application/json',
                    'User-Agent': 'MCP-Toolkit/1.0.0'
                },
                signal: AbortSignal.timeout(this.defaultTimeout)
            };

            if (method === "GET" && Object.keys(params).length > 0) {
                const queryParams = new URLSearchParams();
                Object.entries(params).forEach(([key, value]) => {
                    if (value !== null && value !== undefined) {
                        queryParams.append(key, String(value));
                    }
                });
                fetchUrl = `${url}?${queryParams.toString()}`;
            } else if (method === "POST") {
                fetchOptions.body = JSON.stringify(params);
            }

            const response = await fetch(fetchUrl, fetchOptions);

            if (!response.ok) {
                const errorText = await response.text();
                logger.error(`API error: ${response.status} - ${errorText}`);
                throw new Error(`API error: ${response.status} - ${response.statusText}`);
            }

            const data = await response.json();
            return data;

        } catch (error) {
            if (error instanceof Error) {
                if (error.name === 'AbortError') {
                    throw new Error('Request timed out');
                }
                throw error;
            }
            throw new Error('Unknown error occurred during API request');
        }
    }

    /**
     * Discover available endpoints
     */
    getAvailableEndpoints(): typeof AVAILABLE_ENDPOINTS {
        return AVAILABLE_ENDPOINTS;
    }
}

export function registerXTwitterUnifiedTool(server: McpServer) {
    const client = new XTwitterUnifiedClient();

    // Register the enhanced unified tool with query builder
    server.tool(
        "x-twitter-api",
        "Access any X/Twitter API endpoint through TwitterAPI.io with built-in query optimization. Use 'discover' to list endpoints, 'build-query' to construct optimized queries, or direct endpoint names for API calls.",
        {
            endpoint: z.string().describe(
                "API endpoint name (e.g., 'tweet/advanced_search'), 'discover' to list endpoints, or 'build-query' to construct optimized queries"
            ),
            method: z.enum(["GET", "POST"]).optional().default("GET").describe(
                "HTTP method (default: GET)"
            ),
            params: z.record(z.any()).optional().default({}).describe(
                "Parameters for the API call. For advanced_search: use 'query' with industry keywords, qualifiers, and exclusions"
            ),
            format: z.enum(["raw", "simplified", "analyzed"]).optional().default("simplified").describe(
                "Response format: raw (full API response), simplified (cleaned data), analyzed (with insights)"
            ),
            // Query builder specific parameters
            queryBuilder: z.object({
                action: z.enum(["build", "template", "industry", "authority", "discussion"]).describe(
                    "Query builder action: build (custom query), template (use template), industry (industry-specific), authority (find experts), discussion (find discussions)"
                ),
                keywords: z.array(z.string()).optional().describe("Keywords for query building"),
                industry: z.enum(["SEO", "SaaS", "Tech", "Marketing", "DevOps"]).optional().describe("Industry for specialized queries"),
                template: z.enum(["findExperts", "recentNews", "tutorials", "discussions"]).optional().describe("Query template to use"),
                qualityPreset: z.enum(["highEngagement", "moderate", "minimal", "verified"]).optional().describe("Quality filter preset"),
                excludeNoise: z.boolean().optional().default(true).describe("Whether to exclude spam and promotional content"),
                topic: z.string().optional().describe("Topic for template-based queries")
            }).optional().describe("Query builder configuration for constructing optimized search queries")
        },
        async (args) => {
            try {
                // Handle query building
                if (args.endpoint === "build-query" && args.queryBuilder) {
                    const qb = args.queryBuilder;
                    let query = "";
                    let description = "";

                    switch (qb.action) {
                        case "build":
                            // Custom query building
                            if (!qb.keywords || qb.keywords.length === 0) {
                                throw new Error("Keywords are required for custom query building");
                            }

                            const qualifiers = qb.qualityPreset ? QUALITY_PRESETS[qb.qualityPreset] : QUALITY_PRESETS.moderate;
                            const exclusions = qb.excludeNoise ? [...EXCLUSION_PATTERNS.spam, ...EXCLUSION_PATTERNS.promotional] : [];

                            query = XTwitterQueryBuilder.buildQuery({
                                keywords: qb.keywords,
                                qualifiers,
                                exclusions
                            });
                            description = `Custom query with keywords: ${qb.keywords.join(", ")}`;
                            break;

                        case "template":
                            // Use query template
                            if (!qb.template || !qb.topic) {
                                throw new Error("Template and topic are required for template-based queries");
                            }

                            query = QUERY_TEMPLATES[qb.template](qb.topic);
                            description = `${qb.template} query for: ${qb.topic}`;
                            break;

                        case "industry":
                            // Industry-specific queries
                            if (!qb.industry) {
                                throw new Error("Industry is required for industry-specific queries");
                            }

                            const industryQueries = XTwitterQueryBuilder.getIndustryQueries(
                                qb.industry,
                                qb.qualityPreset || "moderate"
                            );

                            return {
                                content: [{
                                    type: "text",
                                    text: `# Industry-Specific Queries for ${qb.industry}\n\n${industryQueries.map((q, i) =>
                                        `## Query ${i + 1}\n\`\`\`\n${q}\n\`\`\`\n`
                                    ).join('\n')}\n\n## Usage\nCopy any of these queries and use them with the 'tweet/advanced_search' endpoint.`
                                }]
                            };

                        case "authority":
                            // Find industry authorities
                            if (!qb.industry) {
                                throw new Error("Industry is required for authority search");
                            }

                            query = XTwitterQueryBuilder.buildAuthorityQuery(qb.industry);
                            description = `Authority search for ${qb.industry} industry`;
                            break;

                        case "discussion":
                            // Find discussions
                            if (!qb.topic) {
                                throw new Error("Topic is required for discussion search");
                            }

                            query = XTwitterQueryBuilder.buildDiscussionQuery(qb.topic);
                            description = `Discussion search for: ${qb.topic}`;
                            break;
                    }

                    return {
                        content: [{
                            type: "text",
                            text: `# Query Builder Result\n\n**Action:** ${qb.action}\n**Description:** ${description}\n\n## Generated Query\n\`\`\`\n${query}\n\`\`\`\n\n## Next Steps\n1. Use this query with the 'tweet/advanced_search' endpoint\n2. Example:\n\`\`\`json\n{\n  "endpoint": "tweet/advanced_search",\n  "params": {\n    "query": "${query}",\n    "queryType": "Latest"\n  }\n}\n\`\`\``
                        }]
                    };
                }

                // Handle endpoint discovery
                if (args.endpoint === "discover" || args.endpoint === "list") {
                    const endpoints = client.getAvailableEndpoints();
                    const discoveryText = `# Available X/Twitter API Endpoints\n\n` +
                        Object.entries(endpoints).map(([name, info]: [string, any]) => {
                            let endpointDoc = `## ${name}\n` +
                                `- **Method:** ${info.method}\n` +
                                `- **Description:** ${info.description}\n` +
                                `- **Example params:** \`\`\`json\n${JSON.stringify(info.exampleParams, null, 2)}\n\`\`\`\n`;

                            // Add query guidance for advanced search
                            if (name === "tweet/advanced_search" && info.queryGuidance) {
                                endpointDoc += `\n### Query Construction Guide\n\n`;

                                // Industry Keywords
                                endpointDoc += `#### Industry Keywords (Prioritize These)\n`;
                                Object.entries(info.queryGuidance.industryKeywords).forEach(([industry, keywords]: [string, any]) => {
                                    endpointDoc += `- **${industry}:** ${keywords.core.join(', ')}\n`;
                                });

                                // Examples
                                endpointDoc += `\n#### High-Quality Query Examples\n`;
                                info.queryGuidance.examples.forEach((example: string) => {
                                    endpointDoc += `- \`${example}\`\n`;
                                });
                            }

                            return endpointDoc;
                        }).join('\n') +
                        `\n## Query Builder Features\n` +
                        `Use 'build-query' as the endpoint with queryBuilder parameters to:\n` +
                        `- Build custom queries with industry keywords and quality filters\n` +
                        `- Use pre-built templates (findExperts, recentNews, tutorials, discussions)\n` +
                        `- Generate industry-specific queries (SEO, SaaS, Tech, Marketing, DevOps)\n` +
                        `- Find authorities and high-quality discussions\n\n` +
                        `### Example: Build a custom query\n\`\`\`json\n{\n  "endpoint": "build-query",\n  "queryBuilder": {\n    "action": "build",\n    "keywords": ["SEO", "backlinks"],\n    "qualityPreset": "highEngagement",\n    "excludeNoise": true\n  }\n}\n\`\`\``;

                    return {
                        content: [{
                            type: "text",
                            text: discoveryText
                        }]
                    };
                }

                // Validate endpoint exists
                const endpointInfo = AVAILABLE_ENDPOINTS[args.endpoint as EndpointName];
                const method = args.method || endpointInfo?.method || "GET";

                logger.info(`Calling X/Twitter API: ${args.endpoint}`);

                // Make the API request
                const response = await client.request(
                    args.endpoint,
                    method,
                    args.params
                );

                // Format the response based on the requested format
                let formattedResponse: string;

                switch (args.format) {
                    case "raw":
                        formattedResponse = `# X/Twitter API Response\n\n**Endpoint:** ${args.endpoint}\n**Method:** ${method}\n\n## Raw Response\n\`\`\`json\n${JSON.stringify(response, null, 2)}\n\`\`\``;
                        break;

                    case "analyzed":
                        formattedResponse = formatAnalyzedResponse(args.endpoint, response, args.params);
                        break;

                    case "simplified":
                    default:
                        formattedResponse = formatSimplifiedResponse(args.endpoint, response);
                        break;
                }

                return {
                    content: [{
                        type: "text",
                        text: formattedResponse
                    }]
                };

            } catch (error) {
                logger.error("Error in x-twitter-api tool:", error);
                return {
                    content: [{
                        type: "text",
                        text: `# X/Twitter API Error\n\n**Endpoint:** ${args.endpoint}\n**Error:** ${error instanceof Error ? error.message : "Unknown error occurred"}\n\n## Troubleshooting\n- Check if the endpoint name is correct (use 'discover' to list endpoints)\n- Verify the parameters match the API requirements\n- Ensure your API key is valid and has necessary permissions\n- For query building, ensure required parameters are provided`
                    }]
                };
            }
        }
    );

    logger.info("Enhanced Unified X/Twitter API tool with query builder registered successfully");
}

/**
 * Format response in simplified mode
 */
function formatSimplifiedResponse(endpoint: string, data: any): string {
    let content = `# X/Twitter API Response\n\n**Endpoint:** ${endpoint}\n\n`;

    // Handle different endpoint types
    if ((endpoint === "advanced_search" || endpoint === "tweet/advanced_search") && data.tweets) {
        content += `## Search Results (${data.tweets.length} tweets)\n\n`;

        // Add query quality indicator
        const totalEngagement = data.tweets.reduce((sum: number, t: any) =>
            sum + (t.likeCount || 0) + (t.retweetCount || 0) + (t.replyCount || 0), 0
        );
        const avgEngagement = data.tweets.length > 0 ? Math.round(totalEngagement / data.tweets.length) : 0;

        if (data.tweets.length > 0) {
            content += `📊 **Query Quality Metrics:**\n`;
            content += `- Average engagement per tweet: ${avgEngagement}\n`;
            content += `- Total tweets found: ${data.tweets.length}\n\n`;
        }

        data.tweets.forEach((tweet: any, i: number) => {
            const engagement = (tweet.likeCount || 0) + (tweet.retweetCount || 0) + (tweet.replyCount || 0);
            const engagementIndicator = engagement > 1000 ? "🔥" : engagement > 100 ? "📈" : "";

            content += `### ${i + 1}. @${tweet.author?.userName || 'unknown'} ${engagementIndicator}\n`;
            content += `${tweet.text}\n`;
            content += `💬 ${tweet.replyCount || 0} 🔁 ${tweet.retweetCount || 0} ❤️ ${tweet.likeCount || 0}`;
            content += ` (Total: ${engagement})\n`;
            content += `🔗 ${tweet.url || 'N/A'}\n\n`;
        });
    } else if (endpoint.includes("user") && data.user) {
        const user = data.user;
        content += `## User Profile\n`;
        content += `- **Name:** ${user.name}\n`;
        content += `- **Username:** @${user.userName}\n`;
        content += `- **Followers:** ${user.followers?.toLocaleString() || 0}\n`;
        content += `- **Following:** ${user.following?.toLocaleString() || 0}\n`;
        content += `- **Tweets:** ${user.statusesCount?.toLocaleString() || 0}\n`;
        content += `- **Verified:** ${user.isBlueVerified ? 'Yes' : 'No'}\n`;
        if (user.description) content += `- **Bio:** ${user.description}\n`;
    } else if (data.tweets && Array.isArray(data.tweets)) {
        content += `## Tweets (${data.tweets.length})\n\n`;
        data.tweets.forEach((tweet: any, i: number) => {
            content += `${i + 1}. ${tweet.text?.substring(0, 100)}...\n`;
        });
    } else {
        // Generic formatting for unknown structures
        content += `## Response Data\n\`\`\`json\n${JSON.stringify(data, null, 2)}\n\`\`\``;
    }

    return content;
}

/**
 * Format response with analysis
 */
function formatAnalyzedResponse(endpoint: string, data: any, params: any): string {
    let content = formatSimplifiedResponse(endpoint, data);

    content += `\n## Analysis\n`;

    // Add endpoint-specific analysis
    if ((endpoint === "advanced_search" || endpoint === "tweet/advanced_search") && data.tweets) {
        const totalEngagement = data.tweets.reduce((sum: number, t: any) =>
            sum + (t.likeCount || 0) + (t.retweetCount || 0) + (t.replyCount || 0), 0
        );
        const avgEngagement = data.tweets.length > 0 ? Math.round(totalEngagement / data.tweets.length) : 0;

        content += `### 📊 Search Quality Analysis\n`;
        content += `- **Total tweets:** ${data.tweets.length}\n`;
        content += `- **Total engagement:** ${totalEngagement.toLocaleString()}\n`;
        content += `- **Average engagement:** ${avgEngagement.toLocaleString()}\n`;

        // Quality assessment
        if (avgEngagement > 500) {
            content += `- **Quality Assessment:** 🌟 Excellent (High-authority content)\n`;
        } else if (avgEngagement > 100) {
            content += `- **Quality Assessment:** ✅ Good (Quality content)\n`;
        } else if (avgEngagement > 50) {
            content += `- **Quality Assessment:** 📊 Moderate (Standard content)\n`;
        } else {
            content += `- **Quality Assessment:** ⚠️ Low (Consider adding quality filters)\n`;
        }

        // Find most engaged tweet
        const mostEngaged = data.tweets.reduce((max: any, t: any) => {
            const engagement = (t.likeCount || 0) + (t.retweetCount || 0) + (t.replyCount || 0);
            const maxEngagement = (max.likeCount || 0) + (max.retweetCount || 0) + (max.replyCount || 0);
            return engagement > maxEngagement ? t : max;
        }, data.tweets[0] || {});

        if (mostEngaged.text) {
            content += `- **Most engaged tweet:** "${mostEngaged.text.substring(0, 50)}..." (${((mostEngaged.likeCount || 0) + (mostEngaged.retweetCount || 0) + (mostEngaged.replyCount || 0)).toLocaleString()} interactions)\n`;
        }

        // Extract top keywords from results
        const allText = data.tweets.map((t: any) => t.text).join(' ');
        const allIndustryTerms = Object.values(INDUSTRY_KEYWORDS).flatMap(industry => {
            const terms = [...industry.core];
            // Add all other properties that exist
            Object.entries(industry).forEach(([key, value]) => {
                if (key !== 'core' && Array.isArray(value)) {
                    terms.push(...value);
                }
            });
            return terms;
        });
        const foundTerms = allIndustryTerms.filter(term =>
            new RegExp(`\\b${term}\\b`, 'gi').test(allText)
        );

        if (foundTerms.length > 0) {
            content += `\n### 🔑 Industry Keywords Found\n`;
            content += `${[...new Set(foundTerms)].slice(0, 10).join(', ')}\n`;
        }

        // Query optimization suggestions
        if (avgEngagement < 50 && data.tweets.length > 10) {
            content += `\n### 💡 Query Optimization Suggestions\n`;
            content += `- Add quality filters: \`min_faves:50 min_retweets:10\`\n`;
            content += `- Exclude noise: \`-is:retweet -is:reply\`\n`;
            content += `- Target verified accounts: \`from:verified\`\n`;
            content += `- Use specific industry keywords from the list above\n`;

            // Suggest using the query builder
            content += `\n**Pro tip:** Use the query builder to automatically apply these optimizations:\n`;
            content += `\`\`\`json\n{\n  "endpoint": "build-query",\n  "queryBuilder": {\n    "action": "build",\n    "keywords": ${JSON.stringify(foundTerms.slice(0, 3))},\n    "qualityPreset": "highEngagement",\n    "excludeNoise": true\n  }\n}\n\`\`\``;
        }

        // Analyze query used
        if (params?.query) {
            content += `\n### 🔍 Query Analysis\n`;
            content += `- **Query used:** \`${params.query}\`\n`;

            // Check for quality filters
            const hasQualityFilters = /min_faves:|min_retweets:|min_replies:/.test(params.query);
            const hasExclusions = /-is:retweet|-spam|-promotional/.test(params.query);
            const hasIndustryKeywords = allIndustryTerms.some(term =>
                new RegExp(`\\b${term}\\b`, 'i').test(params.query)
            );

            content += `- **Quality filters:** ${hasQualityFilters ? '✅ Present' : '❌ Missing'}\n`;
            content += `- **Exclusion filters:** ${hasExclusions ? '✅ Present' : '❌ Missing'}\n`;
            content += `- **Industry keywords:** ${hasIndustryKeywords ? '✅ Present' : '⚠️ Could be improved'}\n`;
        }
    } else if (endpoint.includes("user") && data.user) {
        const user = data.user;
        const followerRatio = user.following > 0 ? (user.followers / user.following).toFixed(2) : 'N/A';
        content += `- **Follower/Following ratio:** ${followerRatio}\n`;
        content += `- **Average tweets per day:** ${user.statusesCount && user.createdAt ?
            (user.statusesCount / ((Date.now() - new Date(user.createdAt).getTime()) / (1000 * 60 * 60 * 24))).toFixed(1) : 'N/A'}\n`;
    }

    return content;
}