/**
 * Tool Registry - Central place to register all MCP tools
 */

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { logger } from "../utils.js";
import { registerDeepWikiTool } from "./deepwiki-tool.js";
import { registerXTwitterUnifiedTool } from "./x-twitter-unified.js";
import { registerXTwitterUnifiedTool as registerXTwitterEnhancedTool } from "./x-twitter-unified-enhanced.js";
import { registerBlackHatWorldTool } from "./blackhatworld-tool.js";

// Export query builder for external use
export { XTwitterQueryBuilder } from "./x-twitter-query-builder.js";
export type { QueryBuilderOptions, QueryQualifiers } from "./x-twitter-query-builder.js";

export interface ToolConfig {
  name: string;
  enabled: boolean;
  description: string;
  deprecated?: boolean;
}

export const availableTools: Record<string, ToolConfig> = {
  deepwiki: {
    name: "DeepWiki",
    enabled: true,
    description: "GitHub repository search via DeepWiki API"
  },
  xtwitterUnified: {
    name: "X/Twitter Unified API",
    enabled: true,
    description: "Unified access to all X/Twitter API endpoints through TwitterAPI.io"
  },
  xtwitterEnhanced: {
    name: "X/Twitter Enhanced API",
    enabled: false,
    description: "Enhanced X/Twitter API with integrated query builder for optimized searches"
  },
  blackhatworld: {
    name: "BlackHatWorld",
    enabled: true,
    description: "Search and extract content from BlackHatWorld forums for making money online insights"
  }
};

export async function registerAllTools(server: McpServer) {
  logger.info("Registering MCP tools...");

  let registeredCount = 0;

  // Register DeepWiki tool
  if (availableTools['deepwiki']?.enabled) {
    try {
      registerDeepWikiTool(server);
      registeredCount++;
      logger.info(`✅ ${availableTools['deepwiki'].name} tool registered`);
    } catch (error) {
      logger.error(`❌ Failed to register ${availableTools['deepwiki'].name} tool:`, error);
    }
  }

  // Register X/Twitter Unified tool (new)
  if (availableTools['xtwitterUnified']?.enabled) {
    try {
      registerXTwitterUnifiedTool(server);
      registeredCount++;
      logger.info(`✅ ${availableTools['xtwitterUnified'].name} tool registered`);
    } catch (error) {
      logger.error(`❌ Failed to register ${availableTools['xtwitterUnified'].name} tool:`, error);
    }
  }

  // Register X/Twitter Enhanced tool (with query builder)
  if (availableTools['xtwitterEnhanced']?.enabled) {
    try {
      registerXTwitterEnhancedTool(server);
      registeredCount++;
      logger.info(`✅ ${availableTools['xtwitterEnhanced'].name} tool registered`);
    } catch (error) {
      logger.error(`❌ Failed to register ${availableTools['xtwitterEnhanced'].name} tool:`, error);
    }
  }

  // Register BlackHatWorld tool
  if (availableTools['blackhatworld']?.enabled) {
    try {
      await registerBlackHatWorldTool(server);
      registeredCount++;
      logger.info(`✅ ${availableTools['blackhatworld'].name} tool registered`);
    } catch (error) {
      logger.error(`❌ Failed to register ${availableTools['blackhatworld'].name} tool:`, error);
    }
  }

  logger.info(`🎯 Total tools registered: ${registeredCount}`);
  return registeredCount;
}

export function getEnabledTools(): ToolConfig[] {
  return Object.values(availableTools).filter(tool => tool.enabled);
}

export function getToolStatus(): Record<string, boolean> {
  const status: Record<string, boolean> = {};
  Object.entries(availableTools).forEach(([key, tool]) => {
    status[key] = tool.enabled;
  });
  return status;
}
