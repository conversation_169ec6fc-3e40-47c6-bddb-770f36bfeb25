/**
 * BlackHatWorld Tool - Forum content search and extraction
 */

import { z } from "zod";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { logger } from "../utils.js";

export async function registerBlackHatWorldTool(server: McpServer) {
  // Dynamic import for JavaScript module
  const { default: BHWMCPTools } = await import("../blackhatworld/mcp-tools.js");
  const bhwTools = new BHWMCPTools();

  // Tool 1: bhw-search - Search BlackHatWorld forums
  server.tool(
    "bhw-search",
    "Search BlackHatWorld forums for high-quality discussions about making money online, SEO, affiliate marketing, and digital marketing strategies. Supports intelligent keyword search with relevance ranking and traditional forum browsing by date/activity.",
    {
      query: z.string().optional().describe(`Search keywords (optional). Use 2-4 specific terms for best results.

KEYWORD SEARCH EXAMPLES:
• "Google Ads" - Find Google advertising discussions
• "affiliate marketing" - Discover affiliate strategies
• "PayPal stealth" - Learn stealth account methods
• "dropshipping guide" - Get dropshipping tutorials
• "Facebook banned" - Solve account issues
• "automation tool" - Find useful software
• "crypto affiliate" - Explore crypto opportunities

TIPS FOR HIGH-QUALITY RESULTS:
✓ Use specific terms: "Google Ads threshold" vs "advertising"
✓ Include action words: "guide", "method", "strategy", "fix"
✓ Try brand names: "Shopify", "PayPal", "Instagram"
✓ Use industry terms: "CPA", "PPC", "CTR", "ROAS"
✗ Avoid long phrases: Keep under 4 words
✗ Skip common words: "how to", "make money"

If empty, browses forum sections by date instead of keyword matching.`),
      sections: z.array(z.enum([
        "making-money", "seo", "affiliate-marketing", "social-networking",
        "blogging", "black-hat-seo", "white-hat-seo", "ppc", "copywriting",
        "programming", "design", "marketplace"
      ])).optional().describe(`Forum sections to search (default: ['making-money']). Choose sections that match your topic:

POPULAR COMBINATIONS:
• ["making-money"] - General money-making discussions
• ["making-money", "affiliate-marketing"] - Affiliate strategies
• ["making-money", "ppc"] - Paid advertising (Google/Facebook Ads)
• ["seo", "white-hat-seo"] - SEO strategies and techniques
• ["social-networking", "making-money"] - Social media marketing
• ["programming", "making-money"] - Automation and tools
• ["marketplace"] - Services and tools for sale

TIP: Use 1-3 related sections for focused results, or single section for broader coverage.`),
      last_days: z.union([z.literal(1), z.literal(3), z.literal(7), z.literal(30), z.literal(90), z.literal(365), z.literal("all")]).optional().describe("Days to look back - only for forum browsing (default: 7)"),
      order: z.enum(["post_date", "replies", "views", "relevance", "date"]).optional().describe(`Sort order (auto-selected based on search mode):

KEYWORD SEARCH ORDERS:
• "relevance" - Best keyword matches first (default for keyword search)
• "date" - Newest posts first
• "replies" - Most discussed topics first

FORUM BROWSING ORDERS:
• "post_date" - Latest activity first (default for browsing)
• "replies" - Most popular discussions first
• "views" - Most viewed threads first

TIP: Use "relevance" for keyword search, "replies" for popular topics, "post_date" for latest updates.`),
      direction: z.enum(["desc", "asc"]).optional().describe("Sort direction - only for forum browsing (default: 'desc')"),
      min_replies: z.number().min(0).optional().describe(`Minimum reply count for quality filtering (default: 0):
• 0 - Include all threads (good for new/niche topics)
• 3-5 - Filter for discussed topics (balanced quality)
• 8+ - High-quality, well-discussed content only
• 15+ - Very popular, proven valuable discussions

TIP: Use 5+ for established topics, 0-2 for new trends or specific tools.`),
      limit: z.number().min(1).max(50).optional().describe("Maximum results to return (default: 20, max: 50)")
    },
    async ({ query, sections, last_days, order, direction, min_replies, limit }) => {
      try {
        // Determine search mode and set intelligent defaults
        const isKeywordSearch = query && query.trim().length > 0;
        const defaultOrder = isKeywordSearch ? 'relevance' : 'post_date';

        logger.info("Starting BHW search", {
          query: query || '',
          isKeywordSearch,
          sections: sections || ['making-money'],
          last_days: last_days || 7,
          order: order || defaultOrder,
          direction: direction || 'desc',
          min_replies: min_replies || 0,
          limit: limit || 20
        });

        const result = await bhwTools.bhwSearch({
          query: query || '',
          sections: sections || ['making-money'],
          last_days: last_days || 7,
          order: order || defaultOrder,
          direction: direction || 'desc',
          min_replies: min_replies || 0,
          limit: limit || 20
        });

        if (!result.success) {
          throw new Error(result.error || 'Search failed');
        }

        const { threads, totalFound, filteredCount, returnedCount, searchParams } = result.data;

        // Format results for better readability
        const formattedResults = threads.map((thread: any, index: number) => {
          return `${index + 1}. **${thread.title}**
   📊 ${thread.replies} replies, ${thread.views} views
   👤 Author: ${thread.author}
   📂 Section: ${thread.section}
   🔗 ${thread.url}
   📅 Last activity: ${thread.lastActivity ? new Date(thread.lastActivity).toLocaleDateString() : 'Unknown'}`;
        }).join('\n\n');

        // Create search summary based on mode
        const searchSummary = searchParams.isKeywordSearch ?
          `## Keyword Search Summary
- **Query**: "${searchParams.query}"
- **Sections searched**: ${searchParams.sections.join(', ')}
- **Sort by**: ${searchParams.order}
- **Min replies**: ${searchParams.min_replies}` :
          `## Forum Browse Summary
- **Sections searched**: ${searchParams.sections.join(', ')}
- **Time range**: Last ${searchParams.last_days} days
- **Sort by**: ${searchParams.order} (${searchParams.direction})
- **Min replies**: ${searchParams.min_replies}`;

        const summary = `# BlackHatWorld Search Results

${searchSummary}

## Results Overview
- **Total found**: ${totalFound} threads
- **After filtering**: ${filteredCount} threads
- **Returned**: ${returnedCount} threads

## Threads Found

${formattedResults}

---
*Search completed at ${new Date().toISOString()}*`;

        logger.info("BHW search completed", {
          totalFound,
          filteredCount,
          returnedCount
        });

        return {
          content: [{
            type: "text",
            text: summary
          }]
        };

      } catch (error) {
        logger.error("BHW search failed", { error });

        return {
          content: [{
            type: "text",
            text: `BlackHatWorld search failed: ${error instanceof Error ? error.message : 'Unknown error'}`
          }],
          isError: true
        };
      }
    }
  );

  // Tool 2: bhw-get-content - Get detailed thread content
  server.tool(
    "bhw-get-content",
    "Get detailed content from a specific BlackHatWorld thread, including the main post and replies. Perfect for extracting actionable information and detailed methods.",
    {
      url: z.string().url().describe("BlackHatWorld thread URL (required)"),
      include_replies: z.boolean().optional().describe("Include reply posts (default: true)"),
      max_replies: z.number().min(0).max(100).optional().describe("Maximum replies to return (default: 50)"),
      quality_replies_only: z.boolean().optional().describe("Filter for quality replies only (default: false)")
    },
    async ({ url, include_replies, max_replies, quality_replies_only }) => {
      try {
        logger.info("Getting BHW content", {
          url,
          include_replies: include_replies !== false,
          max_replies: max_replies || 50,
          quality_replies_only: quality_replies_only || false
        });

        const result = await bhwTools.bhwGetContent({
          url,
          include_replies: include_replies !== false,
          max_replies: max_replies || 50,
          quality_replies_only: quality_replies_only || false
        });

        if (!result.success) {
          throw new Error(result.error || 'Content extraction failed');
        }

        const thread = result.data;

        // Format main post
        const mainPostContent = `## Main Post
**Author**: ${thread.mainPost.author} (${thread.mainPost.userTitle})
**Posted**: ${thread.mainPost.date ? new Date(thread.mainPost.date).toLocaleDateString() : 'Unknown'}
**Content Length**: ${thread.mainPost.contentLength} characters
**Likes**: ${thread.mainPost.likes}

### Content
${thread.mainPost.content.replace(/<[^>]*>/g, '').trim()}`;

        // Format replies
        let repliesContent = '';
        if (thread.replies && thread.replies.length > 0) {
          repliesContent = `\n\n## Replies (${thread.filteredReplyCount} shown)

${thread.replies.map((reply: any, index: number) => {
            return `### Reply ${index + 1}
**Author**: ${reply.author} (${reply.userTitle})
**Posted**: ${reply.date ? new Date(reply.date).toLocaleDateString() : 'Unknown'}
**Likes**: ${reply.likes}

${reply.content.replace(/<[^>]*>/g, '').trim()}`;
          }).join('\n\n---\n\n')}`;
        }

        const summary = `# ${thread.title}

**Thread URL**: ${thread.url}
**Thread ID**: ${thread.threadId}
**Total Posts**: ${thread.totalPosts}
**Extracted**: ${new Date(thread.extractedAt).toLocaleString()}

${mainPostContent}${repliesContent}

---
*Content extracted at ${new Date().toISOString()}*`;

        logger.info("BHW content extraction completed", {
          threadId: thread.threadId,
          totalPosts: thread.totalPosts,
          repliesIncluded: thread.filteredReplyCount
        });

        return {
          content: [{
            type: "text",
            text: summary
          }]
        };

      } catch (error) {
        logger.error("BHW content extraction failed", { url, error });

        return {
          content: [{
            type: "text",
            text: `BlackHatWorld content extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`
          }],
          isError: true
        };
      }
    }
  );

  logger.info("BlackHatWorld tools registered");
}
