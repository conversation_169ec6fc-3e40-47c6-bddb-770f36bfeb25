/**
 * X/Twitter Query Builder Helper
 * Provides utilities for constructing high-quality search queries with industry keywords,
 * qualifiers, and exclusions for optimal results
 */

export interface QueryBuilderOptions {
    keywords: string[];
    qualifiers?: QueryQualifiers;
    exclusions?: string[];
    operators?: QueryOperators;
}

export interface QueryQualifiers {
    minLikes?: number;
    minRetweets?: number;
    minReplies?: number;
    language?: string;
    hasMedia?: boolean;
    hasLinks?: boolean;
    isVerified?: boolean;
    fromUser?: string;
    toUser?: string;
    since?: string;
    until?: string;
}

export interface QueryOperators {
    useOR?: boolean;  // Use OR between keywords instead of AND
    exactPhrase?: boolean;  // Wrap keywords in quotes for exact phrase
    groupKeywords?: boolean;  // Group keywords with parentheses
}

// Industry-specific keyword collections
export const INDUSTRY_KEYWORDS = {
    SEO: {
        core: ['SEO', 'SERP', 'SEM', 'search engine optimization'],
        technical: ['CTR', 'CPC', 'PPC', 'CPM', 'ROAS', 'organic traffic'],
        tools: ['GA4', 'GTM', 'GSC', 'Google Analytics', 'Search Console'],
        concepts: ['backlinks', 'keywords', 'ranking', 'indexing', 'crawling', 'schema markup']
    },
    SaaS: {
        core: ['SaaS', 'software as a service', 'cloud software'],
        metrics: ['MRR', 'ARR', 'CAC', 'LTV', 'churn rate', 'retention'],
        growth: ['PLG', 'product led growth', 'freemium', 'trial conversion'],
        business: ['B2B', 'B2C', 'enterprise', 'SMB', 'startup']
    },
    Tech: {
        core: ['API', 'SDK', 'framework', 'library', 'platform'],
        frontend: ['UI', 'UX', 'React', 'Vue', 'Angular', 'frontend'],
        backend: ['backend', 'database', 'server', 'microservices', 'REST', 'GraphQL'],
        emerging: ['AI', 'ML', 'LLM', 'GPT', 'blockchain', 'Web3']
    },
    Marketing: {
        core: ['marketing', 'digital marketing', 'growth marketing'],
        metrics: ['ROI', 'KPI', 'conversion rate', 'CTR', 'engagement'],
        tactics: ['CRO', 'A/B testing', 'funnel optimization', 'personalization'],
        channels: ['content marketing', 'email marketing', 'social media', 'paid ads']
    },
    DevOps: {
        core: ['DevOps', 'CI/CD', 'automation', 'infrastructure'],
        tools: ['Docker', 'Kubernetes', 'Jenkins', 'GitHub Actions'],
        cloud: ['AWS', 'Azure', 'GCP', 'cloud computing'],
        practices: ['monitoring', 'logging', 'deployment', 'scaling']
    }
};

// Common exclusion patterns to filter out noise
export const EXCLUSION_PATTERNS = {
    spam: ['-spam', '-"follow me"', '-"follow back"', '-"check my bio"'],
    promotional: ['-giveaway', '-contest', '-"win a"', '-promotion', '-discount'],
    lowQuality: ['-"just posted"', '-"link in bio"', '-"check out"', '-"new video"'],
    crypto: ['-crypto', '-NFT', '-"to the moon"', '-airdrop'],
    adult: ['-NSFW', '-"18+"', '-OnlyFans']
};

// Quality filter presets
export const QUALITY_PRESETS = {
    highEngagement: {
        minLikes: 100,
        minRetweets: 20,
        language: 'en'
    },
    moderate: {
        minLikes: 50,
        minRetweets: 10,
        language: 'en'
    },
    minimal: {
        minLikes: 10,
        language: 'en'
    },
    verified: {
        isVerified: true,
        minLikes: 20,
        language: 'en'
    }
};

export class XTwitterQueryBuilder {
    /**
     * Build a high-quality search query with industry keywords and filters
     */
    static buildQuery(options: QueryBuilderOptions): string {
        const parts: string[] = [];

        // Add keywords
        const keywords = this.formatKeywords(options.keywords, options.operators);
        if (keywords) {
            parts.push(keywords);
        }

        // Add qualifiers
        if (options.qualifiers) {
            const qualifierStrings = this.buildQualifiers(options.qualifiers);
            parts.push(...qualifierStrings);
        }

        // Add exclusions
        if (options.exclusions && options.exclusions.length > 0) {
            parts.push(...options.exclusions);
        }

        return parts.join(' ').trim();
    }

    /**
     * Format keywords based on operators
     */
    private static formatKeywords(keywords: string[], operators?: QueryOperators): string {
        if (!keywords || keywords.length === 0) return '';

        let formattedKeywords = keywords.map(keyword => {
            // Handle multi-word keywords
            if (keyword.includes(' ') && operators?.exactPhrase !== false) {
                return `"${keyword}"`;
            }
            return keyword;
        });

        // Join keywords with appropriate operator
        const joinOperator = operators?.useOR ? ' OR ' : ' ';
        let result = formattedKeywords.join(joinOperator);

        // Group keywords if requested
        if (operators?.groupKeywords && keywords.length > 1) {
            result = `(${result})`;
        }

        return result;
    }

    /**
     * Build qualifier strings from options
     */
    private static buildQualifiers(qualifiers: QueryQualifiers): string[] {
        const parts: string[] = [];

        // Engagement qualifiers
        if (qualifiers.minLikes !== undefined) {
            parts.push(`min_faves:${qualifiers.minLikes}`);
        }
        if (qualifiers.minRetweets !== undefined) {
            parts.push(`min_retweets:${qualifiers.minRetweets}`);
        }
        if (qualifiers.minReplies !== undefined) {
            parts.push(`min_replies:${qualifiers.minReplies}`);
        }

        // Content qualifiers
        if (qualifiers.hasMedia !== undefined) {
            parts.push(qualifiers.hasMedia ? 'has:media' : '-has:media');
        }
        if (qualifiers.hasLinks !== undefined) {
            parts.push(qualifiers.hasLinks ? 'has:links' : '-has:links');
        }

        // User qualifiers
        if (qualifiers.isVerified) {
            parts.push('from:verified');
        }
        if (qualifiers.fromUser) {
            parts.push(`from:${qualifiers.fromUser}`);
        }
        if (qualifiers.toUser) {
            parts.push(`to:${qualifiers.toUser}`);
        }

        // Time qualifiers
        if (qualifiers.since) {
            parts.push(`since:${qualifiers.since}`);
        }
        if (qualifiers.until) {
            parts.push(`until:${qualifiers.until}`);
        }

        // Language qualifier
        if (qualifiers.language) {
            parts.push(`lang:${qualifiers.language}`);
        }

        // Always exclude retweets for quality
        parts.push('-is:retweet');

        return parts;
    }

    /**
     * Get industry-specific query suggestions
     */
    static getIndustryQueries(industry: keyof typeof INDUSTRY_KEYWORDS, preset: keyof typeof QUALITY_PRESETS = 'moderate'): string[] {
        const industryData = INDUSTRY_KEYWORDS[industry];
        const qualityPreset = QUALITY_PRESETS[preset];
        const queries: string[] = [];

        // Core concepts query
        queries.push(this.buildQuery({
            keywords: industryData.core.slice(0, 2),
            qualifiers: qualityPreset,
            exclusions: EXCLUSION_PATTERNS.spam
        }));

        // Trending topics query (OR operator for broader results)
        queries.push(this.buildQuery({
            keywords: [...industryData.core.slice(0, 1), ...Object.values(industryData).flat().slice(0, 2)],
            qualifiers: { ...qualityPreset, minLikes: 50 },
            operators: { useOR: true },
            exclusions: [...EXCLUSION_PATTERNS.spam, ...EXCLUSION_PATTERNS.promotional]
        }));

        // High-authority content query
        queries.push(this.buildQuery({
            keywords: industryData.core.slice(0, 1),
            qualifiers: { ...qualityPreset, isVerified: true, minLikes: 100 },
            exclusions: EXCLUSION_PATTERNS.lowQuality
        }));

        return queries;
    }

    /**
     * Build a query for finding industry authorities
     */
    static buildAuthorityQuery(industry: keyof typeof INDUSTRY_KEYWORDS): string {
        const keywords = INDUSTRY_KEYWORDS[industry].core.slice(0, 2);

        return this.buildQuery({
            keywords,
            qualifiers: {
                isVerified: true,
                minLikes: 200,
                minRetweets: 50,
                language: 'en'
            },
            exclusions: [...EXCLUSION_PATTERNS.spam, ...EXCLUSION_PATTERNS.promotional]
        });
    }

    /**
     * Build a query for recent high-quality discussions
     */
    static buildDiscussionQuery(topic: string, hoursAgo: number = 24): string {
        const dateStr = new Date(Date.now() - hoursAgo * 60 * 60 * 1000).toISOString();
        const since = dateStr.split('T')[0] || dateStr.substring(0, 10);

        return this.buildQuery({
            keywords: [topic],
            qualifiers: {
                minReplies: 5,
                minLikes: 20,
                since: since,
                language: 'en'
            },
            exclusions: EXCLUSION_PATTERNS.spam
        });
    }
}

// Export example queries for LLM reference
export const EXAMPLE_QUERIES = {
    seoTrends: XTwitterQueryBuilder.buildQuery({
        keywords: ['SEO trends', '2025'],
        qualifiers: QUALITY_PRESETS.highEngagement,
        exclusions: [...EXCLUSION_PATTERNS.spam, ...EXCLUSION_PATTERNS.promotional]
    }),

    saasGrowth: XTwitterQueryBuilder.buildQuery({
        keywords: ['SaaS', 'growth', 'MRR'],
        qualifiers: { ...QUALITY_PRESETS.moderate, hasLinks: true },
        operators: { useOR: true },
        exclusions: EXCLUSION_PATTERNS.crypto
    }),

    aiMarketing: XTwitterQueryBuilder.buildQuery({
        keywords: ['AI', 'marketing automation'],
        qualifiers: QUALITY_PRESETS.verified,
        operators: { exactPhrase: true },
        exclusions: [...EXCLUSION_PATTERNS.spam, ...EXCLUSION_PATTERNS.crypto]
    }),

    technicalSEO: XTwitterQueryBuilder.buildQuery({
        keywords: ['Core Web Vitals', 'page speed', 'SEO'],
        qualifiers: { minLikes: 50, hasMedia: true, language: 'en' },
        operators: { groupKeywords: true },
        exclusions: EXCLUSION_PATTERNS.lowQuality
    })
};

// Export query templates for common use cases
export const QUERY_TEMPLATES = {
    findExperts: (topic: string) => XTwitterQueryBuilder.buildQuery({
        keywords: [topic],
        qualifiers: { isVerified: true, minLikes: 100, language: 'en' },
        exclusions: EXCLUSION_PATTERNS.spam
    }),

    recentNews: (topic: string) => {
        const dateStr = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
        const since = dateStr.split('T')[0] || dateStr.substring(0, 10);
        return XTwitterQueryBuilder.buildQuery({
            keywords: [topic, 'news'],
            qualifiers: {
                since: since,
                minLikes: 50,
                hasLinks: true,
                language: 'en'
            },
            exclusions: [...EXCLUSION_PATTERNS.spam, ...EXCLUSION_PATTERNS.promotional]
        });
    },

    tutorials: (technology: string) => XTwitterQueryBuilder.buildQuery({
        keywords: [technology, 'tutorial', 'guide'],
        qualifiers: { minLikes: 20, hasLinks: true, language: 'en' },
        operators: { useOR: true },
        exclusions: EXCLUSION_PATTERNS.promotional
    }),

    discussions: (topic: string) => XTwitterQueryBuilder.buildQuery({
        keywords: [topic],
        qualifiers: { minReplies: 10, minLikes: 30, language: 'en' },
        exclusions: [...EXCLUSION_PATTERNS.spam, '-is:reply']
    })
};