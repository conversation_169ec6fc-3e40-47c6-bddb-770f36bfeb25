/**
 * Unified X/Twitter API Tool
 * Provides access to any TwitterAPI.io endpoint through a single flexible interface
 */

import { z } from "zod";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { logger } from "../utils.js";

// Available TwitterAPI.io endpoints with their descriptions
export const AVAILABLE_ENDPOINTS = {
    // User endpoints
    "user/batch_get_user_by_userids": {
        method: "GET",
        description: "Get multiple users by their user IDs",
        exampleParams: { userIds: ["123456", "789012"] }
    },
    "user/get_user_by_username": {
        method: "GET",
        description: "Get user information by username",
        exampleParams: { username: "elonmusk" }
    },
    "user/get_user_last_tweets": {
        method: "GET",
        description: "Get a user's most recent tweets",
        exampleParams: { username: "elonmusk", count: 20 }
    },
    "user/get_user_followings": {
        method: "GET",
        description: "Get list of users that a user follows",
        exampleParams: { username: "elonmusk", cursor: null }
    },
    "user/get_user_followers": {
        method: "GET",
        description: "Get list of users that follow a user",
        exampleParams: { username: "elonmusk", cursor: null }
    },
    "user/get_user_mention": {
        method: "GET",
        description: "Get user mentions",
        exampleParams: { username: "elonmusk", cursor: null }
    },
    "user/search_user": {
        method: "GET",
        description: "Search user by keyword",
        exampleParams: { query: "elon", cursor: null }
    },

    // Tweet endpoints
    "tweet/get_tweet_by_ids": {
        method: "GET",
        description: "Get tweets by their IDs",
        exampleParams: { tweetIds: ["1234567890", "0987654321"] }
    },
    "tweet/get_tweet_reply": {
        method: "GET",
        description: "Get replies to a specific tweet",
        exampleParams: { tweetId: "1234567890", cursor: null }
    },
    "tweet/get_tweet_quote": {
        method: "GET",
        description: "Get tweet quotations",
        exampleParams: { tweetId: "1234567890", cursor: null }
    },
    "tweet/get_tweet_retweeter": {
        method: "GET",
        description: "Get tweet retweeters",
        exampleParams: { tweetId: "1234567890", cursor: null }
    },
    "tweet/get_tweet_thread_context": {
        method: "GET",
        description: "Get the full thread context of a tweet",
        exampleParams: { tweetId: "1234567890" }
    },
    "tweet/get_article": {
        method: "GET",
        description: "Get article content from a tweet",
        exampleParams: { tweetId: "1234567890" }
    },
    "tweet/advanced_search": {
        method: "GET",
        description: "Advanced tweet search with query syntax. Supports industry keywords, qualifiers, and exclusions for precise results.",
        exampleParams: {
            query: "from:elonmusk min_likes:1000",
            queryType: "Latest",
            cursor: null
        },
        queryGuidance: {
            industryKeywords: {
                SEO: ["SEO", "SERP", "SEM", "CTR", "CPC", "PPC", "GA4", "GTM", "backlinks", "keywords", "ranking"],
                SaaS: ["SaaS", "MVP", "MRR", "ARR", "CAC", "LTV", "PLG", "B2B", "B2C", "churn", "retention"],
                Tech: ["API", "SDK", "UI", "UX", "AI", "ML", "LLM", "GPT", "blockchain", "cloud", "DevOps"],
                Marketing: ["ROI", "KPI", "CRO", "A/B testing", "conversion", "funnel", "growth hacking", "viral"]
            },
            qualifiers: {
                engagement: "min_faves:50 min_retweets:10 min_replies:5",
                quality: "-is:retweet -is:reply lang:en",
                timeRange: "since:2024-01-01 until:2024-12-31",
                media: "has:media has:images has:video",
                links: "has:links -has:links",
                verified: "from:verified"
            },
            exclusions: {
                spam: "-filter:spam -filter:low_quality",
                promotional: '-"giveaway" -"win" -"contest" -"promotion"',
                noise: '-"just posted" -"check out" -"link in bio"'
            },
            examples: [
                'SEO "best practices" min_faves:100 -is:retweet lang:en',
                'SaaS growth OR "growth hacking" from:verified min_retweets:50',
                '(AI OR "artificial intelligence") AND marketing -"crypto" -"NFT"',
                '"product led growth" OR PLG min_faves:20 has:links'
            ]
        }
    },

    // List endpoints
    "list/get_list_tweet": {
        method: "GET",
        description: "Get tweets from a specific list",
        exampleParams: { listId: "1234567890", cursor: null }
    },
    "list/get_list_followers": {
        method: "GET",
        description: "Get list followers",
        exampleParams: { listId: "1234567890", cursor: null }
    },
    "list/get_list_members": {
        method: "GET",
        description: "Get list members",
        exampleParams: { listId: "1234567890", cursor: null }
    },

    // Community endpoints
    "community/get_community_by_id": {
        method: "GET",
        description: "Get community info by ID",
        exampleParams: { communityId: "1234567890" }
    },
    "community/get_community_members": {
        method: "GET",
        description: "Get community members",
        exampleParams: { communityId: "1234567890", cursor: null }
    },
    "community/get_community_moderators": {
        method: "GET",
        description: "Get community moderators",
        exampleParams: { communityId: "1234567890", cursor: null }
    },
    "community/get_community_tweets": {
        method: "GET",
        description: "Get tweets from a specific community",
        exampleParams: { communityId: "1234567890", cursor: null }
    },

    // Trend endpoints
    "trend/get_trends": {
        method: "GET",
        description: "Get trending topics",
        exampleParams: { location: "worldwide" }
    }
} as const;

type EndpointName = keyof typeof AVAILABLE_ENDPOINTS;

export class XTwitterUnifiedClient {
    private readonly apiKey: string;
    private readonly baseUrl = "https://api.twitterapi.io/twitter";
    private readonly defaultTimeout = 30000;

    constructor() {
        this.apiKey = process.env["X_API_KEY"] || "";
        if (!this.apiKey) {
            throw new Error(
                "X_API_KEY environment variable is required. " +
                "Please set it in your MCP configuration file under the 'env' field."
            );
        }
    }

    /**
     * Make a request to any TwitterAPI.io endpoint
     */
    async request(
        endpoint: string,
        method: string = "GET",
        params: Record<string, any> = {}
    ): Promise<any> {
        // Construct the full URL
        const url = endpoint.startsWith("http")
            ? endpoint
            : `${this.baseUrl}/${endpoint.replace(/^\//, "")}`;

        logger.info(`Making ${method} request to: ${url}`);
        logger.debug(`Parameters:`, params);

        try {
            let fetchUrl = url;
            let fetchOptions: RequestInit = {
                method,
                headers: {
                    'X-API-Key': this.apiKey,
                    'Content-Type': 'application/json',
                    'User-Agent': 'MCP-Toolkit/1.0.0'
                },
                signal: AbortSignal.timeout(this.defaultTimeout)
            };

            if (method === "GET" && Object.keys(params).length > 0) {
                const queryParams = new URLSearchParams();
                Object.entries(params).forEach(([key, value]) => {
                    if (value !== null && value !== undefined) {
                        queryParams.append(key, String(value));
                    }
                });
                fetchUrl = `${url}?${queryParams.toString()}`;
            } else if (method === "POST") {
                fetchOptions.body = JSON.stringify(params);
            }

            const response = await fetch(fetchUrl, fetchOptions);

            if (!response.ok) {
                const errorText = await response.text();
                logger.error(`API error: ${response.status} - ${errorText}`);
                throw new Error(`API error: ${response.status} - ${response.statusText}`);
            }

            const data = await response.json();
            return data;

        } catch (error) {
            if (error instanceof Error) {
                if (error.name === 'AbortError') {
                    throw new Error('Request timed out');
                }
                throw error;
            }
            throw new Error('Unknown error occurred during API request');
        }
    }

    /**
     * Discover available endpoints
     */
    getAvailableEndpoints(): typeof AVAILABLE_ENDPOINTS {
        return AVAILABLE_ENDPOINTS;
    }
}

export function registerXTwitterUnifiedTool(server: McpServer) {
    const client = new XTwitterUnifiedClient();

    // Register the unified tool
    server.tool(
        "x-twitter-api",
        "Access any X/Twitter API endpoint through TwitterAPI.io. Use 'discover' as endpoint to list all available endpoints. For advanced searches, prioritize industry keywords and use qualifiers/exclusions for high-quality results.",
        {
            endpoint: z.string().describe(
                "API endpoint name (e.g., 'tweet/advanced_search', 'user/get_user_by_username') or 'discover' to list all endpoints with query guidance"
            ),
            method: z.enum(["GET", "POST"]).optional().default("GET").describe(
                "HTTP method (default: GET)"
            ),
            params: z.record(z.any()).optional().default({}).describe(
                "Parameters for the API call. For advanced_search: use 'query' with industry keywords, qualifiers (min_faves:X, -is:retweet), and exclusions (-spam -promotional)"
            ),
            format: z.enum(["raw", "simplified", "analyzed"]).optional().default("simplified").describe(
                "Response format: raw (full API response), simplified (cleaned data), analyzed (with insights)"
            )
        },
        async (args) => {
            try {
                // Handle endpoint discovery
                if (args.endpoint === "discover" || args.endpoint === "list") {
                    const endpoints = client.getAvailableEndpoints();
                    const discoveryText = `# Available X/Twitter API Endpoints\n\n` +
                        Object.entries(endpoints).map(([name, info]: [string, any]) => {
                            let endpointDoc = `## ${name}\n` +
                                `- **Method:** ${info.method}\n` +
                                `- **Description:** ${info.description}\n` +
                                `- **Example params:** \`\`\`json\n${JSON.stringify(info.exampleParams, null, 2)}\n\`\`\`\n`;

                            // Add query guidance for advanced search
                            if (name === "tweet/advanced_search" && info.queryGuidance) {
                                endpointDoc += `\n### Query Construction Guide\n\n`;

                                // Industry Keywords
                                endpointDoc += `#### Industry Keywords (Prioritize These)\n`;
                                Object.entries(info.queryGuidance.industryKeywords).forEach(([industry, keywords]: [string, any]) => {
                                    endpointDoc += `- **${industry}:** ${keywords.join(', ')}\n`;
                                });

                                // Qualifiers
                                endpointDoc += `\n#### Quality Qualifiers\n`;
                                Object.entries(info.queryGuidance.qualifiers).forEach(([type, example]: [string, any]) => {
                                    endpointDoc += `- **${type}:** \`${example}\`\n`;
                                });

                                // Exclusions
                                endpointDoc += `\n#### Exclusion Filters (Remove Noise)\n`;
                                Object.entries(info.queryGuidance.exclusions).forEach(([type, example]: [string, any]) => {
                                    endpointDoc += `- **${type}:** \`${example}\`\n`;
                                });

                                // Examples
                                endpointDoc += `\n#### High-Quality Query Examples\n`;
                                info.queryGuidance.examples.forEach((example: string) => {
                                    endpointDoc += `- \`${example}\`\n`;
                                });
                            }

                            return endpointDoc;
                        }).join('\n') +
                        `\n## Best Practices for Search Queries\n` +
                        `1. **Use Industry Keywords**: Start with specific industry terms (SEO, SaaS, API, etc.)\n` +
                        `2. **Apply Quality Filters**: Add min_faves:50, min_retweets:10 for high-quality content\n` +
                        `3. **Exclude Noise**: Use -is:retweet, -spam, -promotional filters\n` +
                        `4. **Combine Terms**: Use OR for alternatives, AND for requirements\n` +
                        `5. **Target Authorities**: Use from:username or from:verified for expert content\n\n` +
                        `## Usage Example\n\`\`\`json\n{\n  "endpoint": "tweet/advanced_search",\n  "params": { \n    "query": "SEO \\"best practices\\" min_faves:100 -is:retweet lang:en",\n    "queryType": "Top"\n  }\n}\n\`\`\``;

                    return {
                        content: [{
                            type: "text",
                            text: discoveryText
                        }]
                    };
                }

                // Validate endpoint exists
                const endpointInfo = AVAILABLE_ENDPOINTS[args.endpoint as EndpointName];
                const method = args.method || endpointInfo?.method || "GET";

                logger.info(`Calling X/Twitter API: ${args.endpoint}`);

                // Enhance query parameters for advanced search
                let enhancedParams = { ...args.params };
                if ((args.endpoint === "tweet/advanced_search" || args.endpoint === "advanced_search") && enhancedParams['query']) {
                    logger.info(`Original query: ${enhancedParams['query']}`);
                    // Log the query for debugging but don't modify it - let the LLM construct it properly
                }

                // Make the API request
                const response = await client.request(
                    args.endpoint,
                    method,
                    enhancedParams
                );

                // Format the response based on the requested format
                let formattedResponse: string;

                switch (args.format) {
                    case "raw":
                        formattedResponse = `# X/Twitter API Response\n\n**Endpoint:** ${args.endpoint}\n**Method:** ${method}\n\n## Raw Response\n\`\`\`json\n${JSON.stringify(response, null, 2)}\n\`\`\``;
                        break;

                    case "analyzed":
                        formattedResponse = formatAnalyzedResponse(args.endpoint, response);
                        break;

                    case "simplified":
                    default:
                        formattedResponse = formatSimplifiedResponse(args.endpoint, response);
                        break;
                }

                return {
                    content: [{
                        type: "text",
                        text: formattedResponse
                    }]
                };

            } catch (error) {
                logger.error("Error in x-twitter-api tool:", error);
                return {
                    content: [{
                        type: "text",
                        text: `# X/Twitter API Error\n\n**Endpoint:** ${args.endpoint}\n**Error:** ${error instanceof Error ? error.message : "Unknown error occurred"}\n\n## Troubleshooting\n- Check if the endpoint name is correct (use 'discover' to list endpoints)\n- Verify the parameters match the API requirements\n- Ensure your API key is valid and has necessary permissions`
                    }]
                };
            }
        }
    );

    logger.info("Unified X/Twitter API tool registered successfully");
}

/**
 * Format response in simplified mode
 */
function formatSimplifiedResponse(endpoint: string, data: any): string {
    let content = `# X/Twitter API Response\n\n**Endpoint:** ${endpoint}\n\n`;

    // Handle different endpoint types
    if ((endpoint === "advanced_search" || endpoint === "tweet/advanced_search") && data.tweets) {
        content += `## Search Results (${data.tweets.length} tweets)\n\n`;

        // Add query quality indicator
        const totalEngagement = data.tweets.reduce((sum: number, t: any) =>
            sum + (t.likeCount || 0) + (t.retweetCount || 0) + (t.replyCount || 0), 0
        );
        const avgEngagement = data.tweets.length > 0 ? Math.round(totalEngagement / data.tweets.length) : 0;

        if (data.tweets.length > 0) {
            content += `📊 **Query Quality Metrics:**\n`;
            content += `- Average engagement per tweet: ${avgEngagement}\n`;
            content += `- Total tweets found: ${data.tweets.length}\n\n`;
        }

        data.tweets.forEach((tweet: any, i: number) => {
            const engagement = (tweet.likeCount || 0) + (tweet.retweetCount || 0) + (tweet.replyCount || 0);
            const engagementIndicator = engagement > 1000 ? "🔥" : engagement > 100 ? "📈" : "";

            content += `### ${i + 1}. @${tweet.author?.userName || 'unknown'} ${engagementIndicator}\n`;
            content += `${tweet.text}\n`;
            content += `💬 ${tweet.replyCount || 0} 🔁 ${tweet.retweetCount || 0} ❤️ ${tweet.likeCount || 0}`;
            content += ` (Total: ${engagement})\n`;
            content += `🔗 ${tweet.url || 'N/A'}\n\n`;
        });
    } else if (endpoint.includes("user") && data.user) {
        const user = data.user;
        content += `## User Profile\n`;
        content += `- **Name:** ${user.name}\n`;
        content += `- **Username:** @${user.userName}\n`;
        content += `- **Followers:** ${user.followers?.toLocaleString() || 0}\n`;
        content += `- **Following:** ${user.following?.toLocaleString() || 0}\n`;
        content += `- **Tweets:** ${user.statusesCount?.toLocaleString() || 0}\n`;
        content += `- **Verified:** ${user.isBlueVerified ? 'Yes' : 'No'}\n`;
        if (user.description) content += `- **Bio:** ${user.description}\n`;
    } else if (data.tweets && Array.isArray(data.tweets)) {
        content += `## Tweets (${data.tweets.length})\n\n`;
        data.tweets.forEach((tweet: any, i: number) => {
            content += `${i + 1}. ${tweet.text?.substring(0, 100)}...\n`;
        });
    } else {
        // Generic formatting for unknown structures
        content += `## Response Data\n\`\`\`json\n${JSON.stringify(data, null, 2)}\n\`\`\``;
    }

    return content;
}

/**
 * Format response with analysis
 */
function formatAnalyzedResponse(endpoint: string, data: any): string {
    let content = formatSimplifiedResponse(endpoint, data);

    content += `\n## Analysis\n`;

    // Add endpoint-specific analysis
    if ((endpoint === "advanced_search" || endpoint === "tweet/advanced_search") && data.tweets) {
        const totalEngagement = data.tweets.reduce((sum: number, t: any) =>
            sum + (t.likeCount || 0) + (t.retweetCount || 0) + (t.replyCount || 0), 0
        );
        const avgEngagement = data.tweets.length > 0 ? Math.round(totalEngagement / data.tweets.length) : 0;

        content += `### 📊 Search Quality Analysis\n`;
        content += `- **Total tweets:** ${data.tweets.length}\n`;
        content += `- **Total engagement:** ${totalEngagement.toLocaleString()}\n`;
        content += `- **Average engagement:** ${avgEngagement.toLocaleString()}\n`;

        // Quality assessment
        if (avgEngagement > 500) {
            content += `- **Quality Assessment:** 🌟 Excellent (High-authority content)\n`;
        } else if (avgEngagement > 100) {
            content += `- **Quality Assessment:** ✅ Good (Quality content)\n`;
        } else if (avgEngagement > 50) {
            content += `- **Quality Assessment:** 📊 Moderate (Standard content)\n`;
        } else {
            content += `- **Quality Assessment:** ⚠️ Low (Consider adding quality filters)\n`;
        }

        // Find most engaged tweet
        const mostEngaged = data.tweets.reduce((max: any, t: any) => {
            const engagement = (t.likeCount || 0) + (t.retweetCount || 0) + (t.replyCount || 0);
            const maxEngagement = (max.likeCount || 0) + (max.retweetCount || 0) + (max.replyCount || 0);
            return engagement > maxEngagement ? t : max;
        }, data.tweets[0] || {});

        if (mostEngaged.text) {
            content += `- **Most engaged tweet:** "${mostEngaged.text.substring(0, 50)}..." (${((mostEngaged.likeCount || 0) + (mostEngaged.retweetCount || 0) + (mostEngaged.replyCount || 0)).toLocaleString()} interactions)\n`;
        }

        // Extract top keywords from results
        const allText = data.tweets.map((t: any) => t.text).join(' ');
        const industryTerms = ['SEO', 'SEM', 'SaaS', 'API', 'AI', 'ML', 'B2B', 'B2C', 'ROI', 'KPI', 'MVP', 'PLG'];
        const foundTerms = industryTerms.filter(term =>
            new RegExp(`\\b${term}\\b`, 'gi').test(allText)
        );

        if (foundTerms.length > 0) {
            content += `\n### 🔑 Industry Keywords Found\n`;
            content += `${foundTerms.join(', ')}\n`;
        }

        // Query optimization suggestions
        if (avgEngagement < 50 && data.tweets.length > 10) {
            content += `\n### 💡 Query Optimization Suggestions\n`;
            content += `- Add quality filters: \`min_faves:50 min_retweets:10\`\n`;
            content += `- Exclude noise: \`-is:retweet -is:reply\`\n`;
            content += `- Target verified accounts: \`from:verified\`\n`;
            content += `- Use specific industry keywords from the list above\n`;
        }
    } else if (endpoint.includes("user") && data.user) {
        const user = data.user;
        const followerRatio = user.following > 0 ? (user.followers / user.following).toFixed(2) : 'N/A';
        content += `- **Follower/Following ratio:** ${followerRatio}\n`;
        content += `- **Average tweets per day:** ${user.statusesCount && user.createdAt ?
            (user.statusesCount / ((Date.now() - new Date(user.createdAt).getTime()) / (1000 * 60 * 60 * 24))).toFixed(1) : 'N/A'}\n`;
    }

    return content;
}