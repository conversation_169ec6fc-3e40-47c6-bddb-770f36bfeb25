/**
 * X/Twitter API Helper Functions
 * Optional utilities for working with the unified API tool
 */

import { logger } from "../utils.js";

/**
 * Query Building Helpers
 */
export class XQueryBuilder {
    private parts: string[] = [];

    static create(base?: string): XQueryBuilder {
        const builder = new XQueryBuilder();
        if (base) builder.parts.push(base);
        return builder;
    }

    // User filters
    from(username: string): this {
        this.parts.push(`from:${username}`);
        return this;
    }

    to(username: string): this {
        this.parts.push(`to:${username}`);
        return this;
    }

    mentions(username: string): this {
        this.parts.push(`@${username}`);
        return this;
    }

    // Content filters
    contains(text: string): this {
        if (text.includes(' ')) {
            this.parts.push(`"${text}"`);
        } else {
            this.parts.push(text);
        }
        return this;
    }

    hashtag(tag: string): this {
        this.parts.push(`#${tag.replace('#', '')}`);
        return this;
    }

    url(url: string): this {
        this.parts.push(`url:${url}`);
        return this;
    }

    // Engagement filters
    minLikes(count: number): this {
        this.parts.push(`min_likes:${count}`);
        return this;
    }

    minRetweets(count: number): this {
        this.parts.push(`min_retweets:${count}`);
        return this;
    }

    minReplies(count: number): this {
        this.parts.push(`min_replies:${count}`);
        return this;
    }

    // Time filters
    since(date: string): this {
        this.parts.push(`since:${date}`);
        return this;
    }

    until(date: string): this {
        this.parts.push(`until:${date}`);
        return this;
    }

    // Language filter
    language(lang: string): this {
        this.parts.push(`lang:${lang}`);
        return this;
    }

    // Media filters
    hasImages(): this {
        this.parts.push('has:images');
        return this;
    }

    hasVideos(): this {
        this.parts.push('has:videos');
        return this;
    }

    hasMedia(): this {
        this.parts.push('has:media');
        return this;
    }

    // Logical operators
    or(terms: string[]): this {
        const orQuery = terms.map(term =>
            term.includes(' ') ? `"${term}"` : term
        ).join(' OR ');
        this.parts.push(`(${orQuery})`);
        return this;
    }

    and(terms: string[]): this {
        terms.forEach(term => this.contains(term));
        return this;
    }

    // Exclusion
    exclude(terms: string[]): this {
        terms.forEach(term => {
            this.parts.push(`-${term}`);
        });
        return this;
    }

    // Build the query
    build(): string {
        return this.parts.join(' ');
    }
}

/**
 * Data Extraction Utilities
 */
export class XDataExtractor {
    /**
     * Extract hashtags from tweet text
     */
    static extractHashtags(text: string): string[] {
        const regex = /#[a-zA-Z0-9_]+/g;
        const matches = text.match(regex) || [];
        return matches.map(tag => tag.substring(1));
    }

    /**
     * Extract mentions from tweet text
     */
    static extractMentions(text: string): string[] {
        const regex = /@[a-zA-Z0-9_]+/g;
        const matches = text.match(regex) || [];
        return matches.map(mention => mention.substring(1));
    }

    /**
     * Extract URLs from tweet text
     */
    static extractUrls(text: string): string[] {
        const regex = /https?:\/\/[^\s]+/g;
        return text.match(regex) || [];
    }

    /**
     * Clean tweet text by removing extra whitespace and newlines
     */
    static cleanText(text: string): string {
        return text
            .replace(/\n+/g, ' ')
            .replace(/\s+/g, ' ')
            .trim();
    }

    /**
     * Calculate engagement score
     */
    static calculateEngagement(tweet: any): number {
        return (tweet.likeCount || 0) +
            (tweet.retweetCount || 0) +
            (tweet.replyCount || 0) +
            (tweet.quoteCount || 0);
    }
}

/**
 * Formatting Helpers
 */
export class XFormatter {
    /**
     * Format large numbers with K/M/B suffixes
     */
    static formatNumber(num: number): string {
        if (num >= 1000000000) {
            return (num / 1000000000).toFixed(1) + 'B';
        } else if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }

    /**
     * Format date to relative time
     */
    static formatRelativeTime(dateStr: string): string {
        const date = new Date(dateStr);
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffSecs = Math.floor(diffMs / 1000);
        const diffMins = Math.floor(diffSecs / 60);
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);

        if (diffDays > 0) return `${diffDays}d ago`;
        if (diffHours > 0) return `${diffHours}h ago`;
        if (diffMins > 0) return `${diffMins}m ago`;
        return `${diffSecs}s ago`;
    }

    /**
     * Format tweet for display
     */
    static formatTweet(tweet: any): string {
        const author = tweet.author?.userName || 'unknown';
        const text = XDataExtractor.cleanText(tweet.text || '');
        const likes = XFormatter.formatNumber(tweet.likeCount || 0);
        const retweets = XFormatter.formatNumber(tweet.retweetCount || 0);
        const time = XFormatter.formatRelativeTime(tweet.createdAt);

        return `@${author} • ${time}\n${text}\n💬 ${tweet.replyCount || 0} 🔁 ${retweets} ❤️ ${likes}`;
    }

    /**
     * Format user profile
     */
    static formatUserProfile(user: any): string {
        const lines = [
            `👤 ${user.name} (@${user.userName})`,
            user.isBlueVerified ? '✓ Verified' : '',
            `📊 ${XFormatter.formatNumber(user.followers)} followers • ${XFormatter.formatNumber(user.following)} following`,
            `📝 ${XFormatter.formatNumber(user.statusesCount)} tweets`,
            user.description ? `\n📄 ${user.description}` : '',
            user.location ? `📍 ${user.location}` : '',
            user.url ? `🔗 ${user.url}` : ''
        ].filter(Boolean);

        return lines.join('\n');
    }
}

/**
 * Analysis Functions
 */
export class XAnalyzer {
    /**
     * Analyze sentiment of tweets
     */
    static analyzeSentiment(tweets: any[]): {
        overall: 'positive' | 'negative' | 'neutral' | 'mixed',
        scores: { positive: number, negative: number, neutral: number }
    } {
        const positiveWords = ['love', 'great', 'awesome', 'amazing', 'good', 'best', 'excellent', 'happy', 'wonderful', 'fantastic'];
        const negativeWords = ['hate', 'bad', 'terrible', 'awful', 'worst', 'horrible', 'sad', 'angry', 'disappointed', 'disgusting'];

        let positive = 0;
        let negative = 0;
        let neutral = 0;

        tweets.forEach(tweet => {
            const text = (tweet.text || '').toLowerCase();
            const hasPositive = positiveWords.some(word => text.includes(word));
            const hasNegative = negativeWords.some(word => text.includes(word));

            if (hasPositive && !hasNegative) positive++;
            else if (hasNegative && !hasPositive) negative++;
            else neutral++;
        });

        const total = tweets.length || 1;
        const scores = {
            positive: Math.round((positive / total) * 100),
            negative: Math.round((negative / total) * 100),
            neutral: Math.round((neutral / total) * 100)
        };

        let overall: 'positive' | 'negative' | 'neutral' | 'mixed';
        if (scores.positive > 60) overall = 'positive';
        else if (scores.negative > 60) overall = 'negative';
        else if (scores.neutral > 60) overall = 'neutral';
        else overall = 'mixed';

        return { overall, scores };
    }

    /**
     * Find trending topics in tweets
     */
    static findTrendingTopics(tweets: any[], limit: number = 10): Array<{ topic: string, count: number }> {
        const topicCounts = new Map<string, number>();

        tweets.forEach(tweet => {
            const hashtags = XDataExtractor.extractHashtags(tweet.text || '');
            hashtags.forEach(tag => {
                topicCounts.set(tag, (topicCounts.get(tag) || 0) + 1);
            });
        });

        return Array.from(topicCounts.entries())
            .map(([topic, count]) => ({ topic, count }))
            .sort((a, b) => b.count - a.count)
            .slice(0, limit);
    }

    /**
     * Analyze user influence
     */
    static analyzeUserInfluence(user: any): {
        score: number,
        level: 'micro' | 'mid' | 'macro' | 'mega',
        factors: string[]
    } {
        const factors: string[] = [];
        let score = 0;

        // Follower count (max 40 points)
        if (user.followers > 1000000) {
            score += 40;
            factors.push('1M+ followers');
        } else if (user.followers > 100000) {
            score += 30;
            factors.push('100K+ followers');
        } else if (user.followers > 10000) {
            score += 20;
            factors.push('10K+ followers');
        } else if (user.followers > 1000) {
            score += 10;
            factors.push('1K+ followers');
        }

        // Verification (20 points)
        if (user.isBlueVerified) {
            score += 20;
            factors.push('Verified account');
        }

        // Engagement rate estimate (20 points)
        const avgEngagementRate = user.favouritesCount / (user.statusesCount || 1);
        if (avgEngagementRate > 100) {
            score += 20;
            factors.push('High engagement rate');
        } else if (avgEngagementRate > 50) {
            score += 10;
            factors.push('Good engagement rate');
        }

        // Activity level (20 points)
        const accountAgeDays = (Date.now() - new Date(user.createdAt).getTime()) / (1000 * 60 * 60 * 24);
        const tweetsPerDay = user.statusesCount / accountAgeDays;
        if (tweetsPerDay > 10) {
            score += 20;
            factors.push('Very active');
        } else if (tweetsPerDay > 1) {
            score += 10;
            factors.push('Active');
        }

        // Determine influence level
        let level: 'micro' | 'mid' | 'macro' | 'mega';
        if (score >= 80) level = 'mega';
        else if (score >= 60) level = 'macro';
        else if (score >= 40) level = 'mid';
        else level = 'micro';

        return { score, level, factors };
    }

    /**
     * Generate insights from tweet collection
     */
    static generateInsights(tweets: any[]): string[] {
        const insights: string[] = [];

        // Time analysis
        const hours = tweets.map(t => new Date(t.createdAt).getHours());
        const peakHour = mode(hours);
        if (peakHour !== undefined) {
            insights.push(`Most active posting time: ${peakHour}:00-${peakHour + 1}:00`);
        }

        // Engagement analysis
        const avgEngagement = tweets.reduce((sum, t) => sum + XDataExtractor.calculateEngagement(t), 0) / tweets.length;
        insights.push(`Average engagement per tweet: ${Math.round(avgEngagement)}`);

        // Media usage
        const withMedia = tweets.filter(t => t.entities?.urls?.length > 0).length;
        const mediaPercentage = Math.round((withMedia / tweets.length) * 100);
        insights.push(`${mediaPercentage}% of tweets contain media or links`);

        // Reply behavior
        const replies = tweets.filter(t => t.isReply).length;
        const replyPercentage = Math.round((replies / tweets.length) * 100);
        insights.push(`${replyPercentage}% are replies to other tweets`);

        return insights;
    }
}

/**
 * Utility function to find mode (most common value) in array
 */
function mode(arr: number[]): number | undefined {
    const counts = new Map<number, number>();
    let maxCount = 0;
    let modeValue: number | undefined;

    arr.forEach(val => {
        const count = (counts.get(val) || 0) + 1;
        counts.set(val, count);
        if (count > maxCount) {
            maxCount = count;
            modeValue = val;
        }
    });

    return modeValue;
}

/**
 * Rate limiting helper
 */
export class XRateLimiter {
    private requests: number[] = [];
    private readonly windowMs: number;
    private readonly maxRequests: number;

    constructor(maxRequests: number = 100, windowMs: number = 60000) {
        this.maxRequests = maxRequests;
        this.windowMs = windowMs;
    }

    async checkLimit(): Promise<boolean> {
        const now = Date.now();
        this.requests = this.requests.filter(time => now - time < this.windowMs);

        if (this.requests.length >= this.maxRequests) {
            return false;
        }

        this.requests.push(now);
        return true;
    }

    async waitForLimit(): Promise<void> {
        while (!(await this.checkLimit())) {
            const oldestRequest = Math.min(...this.requests);
            const waitTime = this.windowMs - (Date.now() - oldestRequest) + 100;
            logger.info(`Rate limit reached, waiting ${Math.round(waitTime / 1000)}s`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }
    }
}