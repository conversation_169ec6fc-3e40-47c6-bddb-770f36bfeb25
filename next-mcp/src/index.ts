#!/usr/bin/env node

/**
 * MCP Toolkit - Main Entry Point
 * Personal collection of useful MCP tools
 */

// Load environment variables from .env file (optional)
import dotenv from 'dotenv';
// Only load .env if it exists, don't fail if it doesn't
try {
  dotenv.config();
} catch (error) {
  // .env file not found or not readable, continue with system env vars
}

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { Command } from "commander";
import { logger } from "./utils.js";
import { registerAllTools, getEnabledTools } from "./tools/index.js";

// Parse CLI arguments using commander
const program = new Command()
  .option("--transport <stdio>", "transport type", "stdio")
  .allowUnknownOption() // let MCP Inspector / other wrappers pass through extra flags
  .parse(process.argv);

const cliOptions = program.opts<{
  transport: string;
}>();

// Validate transport option
const allowedTransports = ["stdio"];
if (!allowedTransports.includes(cliOptions.transport)) {
  console.error(
    `Invalid --transport value: '${cliOptions.transport}'. Must be one of: stdio.`
  );
  process.exit(1);
}

// Function to create a new server instance with all tools registered
async function createServerInstance() {
  const enabledTools = getEnabledTools();

  const server = new McpServer({
    name: "MCP Toolkit",
    description: `Personal MCP toolkit with ${enabledTools.length} tools: ${enabledTools.map(t => t.name).join(', ')}`,
    version: "1.0.0",
    capabilities: {
      resources: {},
      tools: {},
    },
  });

  // Register all available tools
  await registerAllTools(server);

  return server;
}

async function main() {
  const transportType = cliOptions.transport;

  if (transportType === "stdio") {
    // Stdio transport
    const server = await createServerInstance();
    const transport = new StdioServerTransport();
    await server.connect(transport);
    console.error("MCP Toolkit running on stdio");
  } else {
    console.error(`Unsupported transport: ${transportType}`);
    process.exit(1);
  }
}

main().catch((error) => {
  logger.error("Unhandled error:", error);
  process.exit(1);
});

export { createServerInstance };
