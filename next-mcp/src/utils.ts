/**
 * Utility functions for the MCP server
 */

import { MCPServerError, ValidationError } from "./types.js";

// ============================================================================
// VALIDATION UTILITIES
// ============================================================================

/**
 * Validates that a number is not zero (useful for division operations)
 */
export function validateNonZero(value: number, fieldName: string = "value"): void {
  if (value === 0) {
    throw new ValidationError(`${fieldName} cannot be zero`);
  }
}

/**
 * Validates that a string is not empty
 */
export function validateNonEmpty(value: string, fieldName: string = "value"): void {
  if (!value || value.trim().length === 0) {
    throw new ValidationError(`${fieldName} cannot be empty`);
  }
}

/**
 * Validates that a value is within a specified range
 */
export function validateRange(
  value: number,
  min: number,
  max: number,
  fieldName: string = "value"
): void {
  if (value < min || value > max) {
    throw new ValidationError(`${fieldName} must be between ${min} and ${max}`);
  }
}

// ============================================================================
// TEXT PROCESSING UTILITIES
// ============================================================================

/**
 * Counts words in a text string
 */
export function countWords(text: string): number {
  return text.trim().split(/\s+/).filter(word => word.length > 0).length;
}

/**
 * Truncates text to a specified length with ellipsis
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) {
    return text;
  }
  return `${text.substring(0, maxLength - 3)}...`;
}

/**
 * Capitalizes the first letter of each word
 */
export function titleCase(text: string): string {
  return text.replace(/\w\S*/g, (txt) =>
    txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
  );
}

// ============================================================================
// MATH UTILITIES
// ============================================================================

/**
 * Safely performs division with zero check
 */
export function safeDivide(a: number, b: number): number {
  validateNonZero(b, "divisor");
  return a / b;
}

/**
 * Rounds a number to specified decimal places
 */
export function roundToDecimals(value: number, decimals: number = 2): number {
  return Math.round(value * Math.pow(10, decimals)) / Math.pow(10, decimals);
}

/**
 * Calculates percentage
 */
export function calculatePercentage(part: number, whole: number): number {
  validateNonZero(whole, "whole");
  return roundToDecimals((part / whole) * 100);
}

// ============================================================================
// DATE/TIME UTILITIES
// ============================================================================

/**
 * Formats a date according to the specified format
 */
export function formatDate(date: Date, format: "iso" | "locale" | "timestamp"): string {
  switch (format) {
    case "iso":
      return date.toISOString();
    case "locale":
      return date.toLocaleString();
    case "timestamp":
      return date.getTime().toString();
    default:
      throw new ValidationError(`Unsupported date format: ${format}`);
  }
}

/**
 * Gets the current time in various formats
 */
export function getCurrentTime(format: "iso" | "locale" | "timestamp" = "iso"): string {
  return formatDate(new Date(), format);
}

/**
 * Calculates time difference in human-readable format
 */
export function getTimeDifference(start: Date, end: Date): string {
  const diffMs = end.getTime() - start.getTime();
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffDays > 0) {
    return `${diffDays} day${diffDays > 1 ? "s" : ""}`;
  } else if (diffHours > 0) {
    return `${diffHours} hour${diffHours > 1 ? "s" : ""}`;
  } else if (diffMinutes > 0) {
    return `${diffMinutes} minute${diffMinutes > 1 ? "s" : ""}`;
  } else {
    return `${diffSeconds} second${diffSeconds > 1 ? "s" : ""}`;
  }
}

// ============================================================================
// ERROR HANDLING UTILITIES
// ============================================================================

/**
 * Wraps a function with error handling for MCP operations
 */
export function withErrorHandling<T extends any[], R>(
  fn: (..._args: T) => R | Promise<R>
): (..._args: T) => Promise<R> {
  return async (..._args: T): Promise<R> => {
    try {
      const result = await fn(..._args);
      return result;
    } catch (error) {
      if (error instanceof MCPServerError) {
        throw error;
      }

      // Convert unknown errors to MCPServerError
      const message = error instanceof Error ? error.message : "Unknown error occurred";
      throw new MCPServerError(message, -32603, { originalError: error });
    }
  };
}

/**
 * Creates a safe async wrapper that catches and logs errors
 */
export function safeAsync<T extends any[], R>(
  fn: (..._args: T) => Promise<R>,
  fallback?: R
): (..._args: T) => Promise<R | undefined> {
  return async (..._args: T): Promise<R | undefined> => {
    try {
      return await fn(..._args);
    } catch (error) {
      console.error("Error in async operation:", error);
      return fallback;
    }
  };
}

// ============================================================================
// LOGGING UTILITIES
// ============================================================================

/**
 * Simple logger that writes to stderr to avoid interfering with MCP protocol
 */
export const logger = {
  info: (message: string, ..._args: any[]) => {
    console.error(`[INFO] ${new Date().toISOString()} ${message}`, ..._args);
  },
  warn: (message: string, ..._args: any[]) => {
    console.error(`[WARN] ${new Date().toISOString()} ${message}`, ..._args);
  },
  error: (message: string, ..._args: any[]) => {
    console.error(`[ERROR] ${new Date().toISOString()} ${message}`, ..._args);
  },
  debug: (message: string, ..._args: any[]) => {
    if (process.env["DEBUG"]) {
      console.error(`[DEBUG] ${new Date().toISOString()} ${message}`, ..._args);
    }
  }
};

// ============================================================================
// MCP DEBUGGING UTILITIES
// ============================================================================

/**
 * Wrapper for MCP tool handlers that adds comprehensive logging
 */
export function debugTool<T extends Record<string, any>>(
  toolName: string,
  handler: (params: T) => Promise<any>
) {
  return async (params: T) => {
    const startTime = Date.now();
    const requestId = Math.random().toString(36).substring(7);

    logger.debug(`[${requestId}] Tool '${toolName}' called with params:`, JSON.stringify(params, null, 2));

    try {
      const result = await handler(params);
      const duration = Date.now() - startTime;

      logger.debug(`[${requestId}] Tool '${toolName}' completed in ${duration}ms`);
      logger.debug(`[${requestId}] Tool '${toolName}' result:`, JSON.stringify(result, null, 2));

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error(`[${requestId}] Tool '${toolName}' failed after ${duration}ms:`, error);
      throw error;
    }
  };
}

/**
 * Wrapper for MCP resource handlers that adds comprehensive logging
 */
export function debugResource<T extends Record<string, any>>(
  resourceName: string,
  handler: (uri: any, params: T) => Promise<any>
) {
  return async (uri: any, params: T) => {
    const startTime = Date.now();
    const requestId = Math.random().toString(36).substring(7);

    logger.debug(`[${requestId}] Resource '${resourceName}' accessed:`, {
      uri: uri.href,
      params: JSON.stringify(params, null, 2)
    });

    try {
      const result = await handler(uri, params);
      const duration = Date.now() - startTime;

      logger.debug(`[${requestId}] Resource '${resourceName}' loaded in ${duration}ms`);

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error(`[${requestId}] Resource '${resourceName}' failed after ${duration}ms:`, error);
      throw error;
    }
  };
}

/**
 * Wrapper for MCP prompt handlers that adds comprehensive logging
 */
export function debugPrompt<T extends Record<string, any>>(
  promptName: string,
  handler: (params: T) => any
) {
  return (params: T) => {
    const requestId = Math.random().toString(36).substring(7);

    logger.debug(`[${requestId}] Prompt '${promptName}' called with params:`, JSON.stringify(params, null, 2));

    try {
      const result = handler(params);
      logger.debug(`[${requestId}] Prompt '${promptName}' generated successfully`);

      return result;
    } catch (error) {
      logger.error(`[${requestId}] Prompt '${promptName}' failed:`, error);
      throw error;
    }
  };
}

/**
 * Performance monitoring utility
 */
export class PerformanceMonitor {
  private static metrics: Map<string, number[]> = new Map();

  static startTimer(operation: string): () => void {
    const startTime = Date.now();

    return () => {
      const duration = Date.now() - startTime;

      if (!this.metrics.has(operation)) {
        this.metrics.set(operation, []);
      }

      this.metrics.get(operation)!.push(duration);
      logger.debug(`Performance: ${operation} took ${duration}ms`);
    };
  }

  static getStats(operation: string): { avg: number; min: number; max: number; count: number } | null {
    const times = this.metrics.get(operation);
    if (!times || times.length === 0) return null;

    return {
      avg: times.reduce((a, b) => a + b, 0) / times.length,
      min: Math.min(...times),
      max: Math.max(...times),
      count: times.length
    };
  }

  static getAllStats(): Record<string, any> {
    const stats: Record<string, any> = {};

    for (const [operation, times] of this.metrics.entries()) {
      if (times.length > 0) {
        stats[operation] = {
          avg: times.reduce((a, b) => a + b, 0) / times.length,
          min: Math.min(...times),
          max: Math.max(...times),
          count: times.length
        };
      }
    }

    return stats;
  }
}
