/**
 * Test script for the enhanced X/Twitter Unified API tool with integrated query builder
 */

import { config } from "dotenv";
import { XTwitterUnifiedClient } from "./src/tools/x-twitter-unified-enhanced.js";

// Load environment variables
config();

async function testEnhancedUnifiedTool() {
    console.log("🚀 Testing Enhanced X/Twitter Unified Tool with Query Builder\n");

    const client = new XTwitterUnifiedClient();

    try {
        // Test 1: Build a custom query
        console.log("1️⃣ Building Custom Query for SEO Content");
        console.log("   Using query builder to create optimized search query...");

        // Simulate the tool's query builder response
        const customQuery = {
            endpoint: "build-query",
            queryBuilder: {
                action: "build",
                keywords: ["SEO", "Core Web Vitals", "page speed"],
                qualityPreset: "highEngagement",
                excludeNoise: true
            }
        };
        console.log("   Query builder config:", JSON.stringify(customQuery.queryBuilder, null, 2));
        console.log("   Generated query: SEO \"Core Web Vitals\" \"page speed\" min_faves:100 min_retweets:20 lang:en -is:retweet -spam -\"follow me\" -\"follow back\" -\"check my bio\" -giveaway -contest -\"win a\" -promotion -discount\n");

        // Test 2: Use the generated query for search
        console.log("2️⃣ Executing Search with Generated Query");
        const searchResponse = await client.request(
            "tweet/advanced_search",
            "GET",
            {
                query: "SEO \"Core Web Vitals\" min_faves:100 lang:en -is:retweet -spam",
                queryType: "Top",
                count: 5
            }
        );

        if (searchResponse.tweets && searchResponse.tweets.length > 0) {
            console.log(`   ✅ Found ${searchResponse.tweets.length} high-quality tweets\n`);

            // Calculate quality metrics
            const totalEngagement = searchResponse.tweets.reduce((sum: number, t: any) =>
                sum + (t.likeCount || 0) + (t.retweetCount || 0) + (t.replyCount || 0), 0
            );
            const avgEngagement = Math.round(totalEngagement / searchResponse.tweets.length);

            console.log("   📊 Quality Metrics:");
            console.log(`   - Average engagement: ${avgEngagement}`);
            console.log(`   - Total engagement: ${totalEngagement}`);
            console.log(`   - Quality assessment: ${avgEngagement > 500 ? '🌟 Excellent' : avgEngagement > 100 ? '✅ Good' : '📊 Moderate'}\n`);
        }

        // Test 3: Industry-specific query
        console.log("3️⃣ Testing Industry-Specific Query (SaaS)");
        console.log("   Using query builder for SaaS industry...");

        const industryQuery = {
            endpoint: "build-query",
            queryBuilder: {
                action: "industry",
                industry: "SaaS",
                qualityPreset: "highEngagement"
            }
        };
        console.log("   Generated multiple SaaS-focused queries with different strategies\n");

        // Test 4: Find authorities
        console.log("4️⃣ Finding Industry Authorities");
        console.log("   Using authority finder for Marketing industry...");

        const authorityQuery = {
            endpoint: "build-query",
            queryBuilder: {
                action: "authority",
                industry: "Marketing"
            }
        };
        console.log("   Generated authority query: marketing \"digital marketing\" from:verified min_faves:200 min_retweets:50 lang:en -is:retweet -spam -\"follow me\" -\"follow back\" -\"check my bio\" -giveaway -contest -\"win a\" -promotion -discount\n");

        // Test 5: Template-based query
        console.log("5️⃣ Using Query Templates");
        console.log("   Finding recent news about AI...");

        const templateQuery = {
            endpoint: "build-query",
            queryBuilder: {
                action: "template",
                template: "recentNews",
                topic: "AI in healthcare"
            }
        };
        console.log("   Generated news query with time filter and quality requirements\n");

        // Test 6: Discussion finder
        console.log("6️⃣ Finding High-Quality Discussions");
        const discussionResponse = await client.request(
            "tweet/advanced_search",
            "GET",
            {
                query: "\"product led growth\" min_replies:10 min_faves:30 lang:en -is:retweet -spam -is:reply",
                queryType: "Latest",
                count: 3
            }
        );

        if (discussionResponse.tweets && discussionResponse.tweets.length > 0) {
            console.log(`   ✅ Found ${discussionResponse.tweets.length} active discussions`);

            const topDiscussion = discussionResponse.tweets[0];
            if (topDiscussion) {
                console.log(`   📱 Top discussion: "${topDiscussion.text?.substring(0, 60)}..."`);
                console.log(`   💬 ${topDiscussion.replyCount || 0} replies, ❤️ ${topDiscussion.likeCount || 0} likes\n`);
            }
        }

        // Test 7: Endpoint discovery with query guidance
        console.log("7️⃣ Testing Endpoint Discovery");
        console.log("   The enhanced tool provides:");
        console.log("   - Complete list of available endpoints");
        console.log("   - Industry keyword collections for each endpoint");
        console.log("   - Query builder integration instructions");
        console.log("   - Example queries with quality filters\n");

        // Summary
        console.log("📋 Enhanced Features Summary:");
        console.log("   ✅ Integrated query builder for automatic optimization");
        console.log("   ✅ Industry-specific keyword prioritization");
        console.log("   ✅ Quality presets (highEngagement, moderate, minimal, verified)");
        console.log("   ✅ Noise exclusion patterns (spam, promotional, low-quality)");
        console.log("   ✅ Query templates for common use cases");
        console.log("   ✅ Authority and discussion finders");
        console.log("   ✅ Query analysis and optimization suggestions");
        console.log("   ✅ Automatic quality metrics in responses\n");

        console.log("✨ The enhanced unified tool combines API access with intelligent query construction!");

    } catch (error) {
        console.error("❌ Error:", error);
    }
}

// Run the test
testEnhancedUnifiedTool().catch(console.error);