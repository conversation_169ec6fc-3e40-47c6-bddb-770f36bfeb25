/**
 * Test script to verify MCP server startup and tool registration
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function testMCPServer() {
  console.log('🧪 Testing MCP Server startup and tool registration...\n');

  return new Promise((resolve, reject) => {
    // Start the MCP server
    const serverProcess = spawn('node', [join(__dirname, 'dist/index.js')], {
      stdio: ['pipe', 'pipe', 'pipe'],
      env: { ...process.env, NODE_ENV: 'test' }
    });

    let output = '';
    let errorOutput = '';
    let hasStarted = false;

    // Capture stdout
    serverProcess.stdout.on('data', (data) => {
      const text = data.toString();
      output += text;
      console.log('📤 STDOUT:', text.trim());
      
      if (text.includes('MCP Toolkit running on stdio')) {
        hasStarted = true;
      }
    });

    // Capture stderr (this is where MCP server logs go)
    serverProcess.stderr.on('data', (data) => {
      const text = data.toString();
      errorOutput += text;
      console.log('📥 STDERR:', text.trim());
      
      if (text.includes('MCP Toolkit running on stdio')) {
        hasStarted = true;
      }
    });

    // Handle process exit
    serverProcess.on('close', (code) => {
      console.log(`\n🏁 Server process exited with code: ${code}`);
      
      if (hasStarted) {
        console.log('✅ MCP Server started successfully!');
        resolve({ success: true, output, errorOutput });
      } else {
        console.log('❌ MCP Server failed to start properly');
        resolve({ success: false, output, errorOutput });
      }
    });

    // Handle errors
    serverProcess.on('error', (error) => {
      console.error('❌ Process error:', error);
      reject(error);
    });

    // Send a simple initialization message to test MCP protocol
    setTimeout(() => {
      console.log('📨 Sending initialization message...');
      
      const initMessage = JSON.stringify({
        jsonrpc: "2.0",
        id: 1,
        method: "initialize",
        params: {
          protocolVersion: "2024-11-05",
          capabilities: {},
          clientInfo: {
            name: "test-client",
            version: "1.0.0"
          }
        }
      }) + '\n';
      
      serverProcess.stdin.write(initMessage);
      
      // Wait a bit then terminate
      setTimeout(() => {
        console.log('🛑 Terminating server...');
        serverProcess.kill('SIGTERM');
      }, 3000);
      
    }, 1000);
  });
}

// Run the test
testMCPServer()
  .then((result) => {
    console.log('\n📊 Test Results:');
    console.log('================');
    console.log(`Success: ${result.success}`);
    
    if (result.errorOutput.includes('BlackHatWorld tool registered')) {
      console.log('✅ BlackHatWorld tool registered successfully');
    } else {
      console.log('❌ BlackHatWorld tool registration not detected');
    }
    
    if (result.errorOutput.includes('DeepWiki tool registered')) {
      console.log('✅ DeepWiki tool registered successfully');
    }
    
    if (result.errorOutput.includes('X/Twitter')) {
      console.log('✅ X/Twitter tool registered successfully');
    }
    
    const toolCount = (result.errorOutput.match(/tool registered/g) || []).length;
    console.log(`📈 Total tools registered: ${toolCount}`);
    
    process.exit(result.success ? 0 : 1);
  })
  .catch((error) => {
    console.error('💥 Test failed:', error);
    process.exit(1);
  });
